# Test Fixes for XML4750 Validation Issues - UPDATED

## 1. <PERSON><PERSON><PERSON> hình kiểm tra cơ bản - Dropdown kiểu so sánh

### Vấn đề:
- <PERSON><PERSON><PERSON> "<PERSON>ểu so sánh" không có dropdown

### Sửa đổi:
- <PERSON><PERSON><PERSON> thiện cấu trúc editor theo <PERSON>bulator documentation
- Thêm `clearable: false` và `searchable: false`
- File: `xml4750/templates/xml4750/validation_config.html` dòng 533-562

### Test:
1. Vào trang Cấu hình kiểm tra
2. Click tab "<PERSON><PERSON><PERSON> hì<PERSON> cơ bản"
3. Click vào cột "Kiểu so sánh" của bất kỳ dòng nào
4. Kiểm tra xem có dropdown với các options: <PERSON><PERSON>h xác, <PERSON><PERSON> dài, <PERSON><PERSON><PERSON><PERSON> gi<PERSON> trị, Mẫu regex, <PERSON><PERSON> sách cho phép, Bỏ qua kiểm tra

## 2. <PERSON><PERSON><PERSON> hì<PERSON> nâng cao - Load đầy đủ XML types và fields

### Vấn đề:
- <PERSON><PERSON><PERSON><PERSON> load đầy đủ các loại XML và các cột trong từng XML
- Giá trị so sánh không rõ ràng

### Sửa đổi:
- Thêm XML0 vào dropdown XML nguồn
- Bổ sung XML4, XML5 vào function getFieldsForXMLType
- Thêm XML6-XML15 với cấu trúc cơ bản
- Thêm XML6-XML15 vào dropdown "XML điều kiện" trong modal thêm điều kiện
- Làm rõ "Giá trị so sánh" là tùy chọn với placeholder hướng dẫn
- File: `xml4750/templates/xml4750/validation_config.html`

### Test:
1. Vào trang Cấu hình kiểm tra
2. Click tab "Cấu hình nâng cao"
3. Click nút "Thêm điều kiện"
4. Kiểm tra dropdown "XML nguồn" có đầy đủ XML0-XML15
5. Kiểm tra dropdown "XML điều kiện" có đầy đủ XML0-XML15
6. Chọn XML1, kiểm tra dropdown "Trường nguồn" có đầy đủ các trường
7. Chọn XML2, XML3, XML4, XML5 và kiểm tra các trường tương ứng
8. Kiểm tra trường "Giá trị so sánh" có label "(tùy chọn)" và placeholder rõ ràng

## 3. Edit/Delete buttons trong list_4750.html

### Vấn đề:
- Khi ở dòng 1 chọn edit/delete thì lại thực hiện trên dòng 2
- Delete không hoạt động đúng với dữ liệu preview vì cố gắng lấy maLK/ngayTao

### Sửa đổi:
- Sửa cách tính uniqueRowIdentifier trong action column formatter
- Sử dụng `row.getIndex()` trực tiếp thay vì tính toán phức tạp
- Tạo function `deleteRowDirectlyFromTabulatorByIndex` riêng cho dữ liệu preview
- Phân biệt rõ ràng giữa dữ liệu preview và database trong delete logic
- File: `static/js/xml4750/tabulator_xml_tables.js` dòng 924-925
- File: `xml4750/templates/xml4750/list_4750.html` dòng 1065-1077, 2662-2724

### Test:
1. Vào trang XML4750
2. Upload file XML (dữ liệu preview)
3. Ở dòng đầu tiên, click nút Edit (biểu tượng bút chì)
4. Kiểm tra xem modal edit có mở với đúng dữ liệu của dòng 1 không
5. Ở dòng đầu tiên, click nút Delete (biểu tượng thùng rác)
6. Kiểm tra xem có confirm delete đúng dòng 1 không và xóa thành công
7. Test tương tự với dữ liệu từ database

## Các file đã sửa đổi:

1. `xml4750/templates/xml4750/validation_config.html`
   - Cải thiện cấu trúc editor cho dropdown kiểu so sánh theo Tabulator docs
   - Xóa function loadFieldsForXmlType trùng lặp
   - Thêm XML0, XML4, XML5 vào dropdown XML nguồn
   - Bổ sung XML4-XML15 vào function getFieldsForXMLType
   - Thêm XML6-XML15 vào dropdown XML điều kiện trong modal
   - Làm rõ trường "Giá trị so sánh" là tùy chọn

2. `static/js/xml4750/tabulator_xml_tables.js`
   - Sửa cách tính uniqueRowIdentifier trong action column formatter
   - Sử dụng row.getIndex() thay vì tính toán phức tạp

3. `xml4750/templates/xml4750/list_4750.html`
   - Tạo function deleteRowDirectlyFromTabulatorByIndex cho dữ liệu preview
   - Sửa logic phân biệt dữ liệu preview vs database trong delete
   - Sử dụng rowIndex cho dữ liệu preview thay vì maLK/ngayTao

## Cách test tổng thể:

1. Restart server
2. Vào trang XML4750 validation config
3. Test từng tab: Cấu hình cơ bản, Cấu hình nâng cao
4. Vào trang XML4750 list
5. Test edit/delete buttons trên các dòng khác nhau
