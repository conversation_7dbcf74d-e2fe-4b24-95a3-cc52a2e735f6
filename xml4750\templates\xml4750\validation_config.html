{% extends 'layouts/base.html' %}
{% load static %}
{% block title %}C<PERSON><PERSON> hình kiểm tra hồ sơ XML{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'AdminLTE-3.0.1/plugins/sweetalert2-theme-bootstrap-4/bootstrap-4.min.css' %}">
<link href="{% static "tabulator/dist/css/tabulator.min.css" %}" rel="stylesheet">
<link href="{% static "tabulator/dist/css/tabulator_bootstrap4.min.css" %}" rel="stylesheet">
<style>
    .tab-content {
        padding: 20px;
        border: 1px solid #dee2e6;
        border-top: none;
        border-radius: 0 0 .25rem .25rem;
    }
    .validation-tree {
        max-height: 600px;
        overflow-y: auto;
    }
    .field-config-container {
        max-height: 600px;
        overflow-y: auto;
    }
    .validation-rule {
        margin-bottom: 10px;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
    }
    .validation-rule-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }
    .validation-rule-body {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
    }
    .validation-rule-item {
        flex: 1;
        min-width: 200px;
    }
    .rule-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        margin-top: 10px;
    }
    .tabulator-tree-level-0 {
        font-weight: bold;
    }
</style>
{% endblock %}

{% block content %}
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Cấu hình kiểm tra hồ sơ XML</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'xml4750:list_xml' %}">Quản lý XML</a></li>
                        <li class="breadcrumb-item active">Cấu hình kiểm tra</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="card">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" id="validationTabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="basic-tab" data-toggle="tab" href="#basic" role="tab" aria-controls="basic" aria-selected="true">Cấu hình cơ bản</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="advanced-tab" data-toggle="tab" href="#advanced" role="tab" aria-controls="advanced" aria-selected="false">Cấu hình nâng cao</a>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="validationTabsContent">
                        <!-- Tab 1: Cấu hình cơ bản -->
                        <div class="tab-pane fade show active" id="basic" role="tabpanel" aria-labelledby="basic-tab">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Cấu hình trường dữ liệu XML</h3>
                                    <div class="card-tools">
                                        <button type="button" id="load-all-fields" class="btn btn-info btn-sm mr-2">
                                            <i class="fas fa-sync"></i> Tải tất cả trường
                                        </button>
                                        <button type="button" id="save-basic-config" class="btn btn-primary btn-sm">
                                            <i class="fas fa-save"></i> Lưu cấu hình
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div id="field-config-table" class="field-config-container"></div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Tab 2: Cấu hình nâng cao -->
                        <div class="tab-pane fade" id="advanced" role="tabpanel" aria-labelledby="advanced-tab">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Quy tắc kiểm tra nâng cao</h3>
                                    <div class="card-tools">
                                        <button type="button" id="add-validation-rule" class="btn btn-success btn-sm">
                                            <i class="fas fa-plus"></i> Thêm quy tắc
                                        </button>
                                        <button type="button" id="save-advanced-config" class="btn btn-primary btn-sm">
                                            <i class="fas fa-save"></i> Lưu cấu hình
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div id="validation-rules-table"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Modal thêm/sửa quy tắc validation -->
<div class="modal fade" id="validation-rule-modal" tabindex="-1" role="dialog" aria-labelledby="validation-rule-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="validation-rule-modal-label">Thêm quy tắc kiểm tra</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="validation-rule-form">
                    <input type="hidden" id="rule-id" name="rule-id">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="source-xml-type">XML nguồn</label>
                                <select class="form-control" id="source-xml-type" name="source-xml-type" required>
                                    <option value="">-- Chọn XML --</option>
                                    <option value="XML0">XML0 - Thông tin checkin</option>
                                    <option value="XML1">XML1 - Thông tin tổng hợp</option>
                                    <option value="XML2">XML2 - Chi phí khám chữa bệnh</option>
                                    <option value="XML3">XML3 - Thông tin thuốc</option>
                                    <option value="XML4">XML4 - Thông tin DVKT</option>
                                    <option value="XML5">XML5 - Thông tin VTYT</option>
                                    <option value="XML4">XML4 - Dịch vụ cận lâm sàng</option>
                                    <option value="XML5">XML5 - Diễn biến bệnh</option>
                                    <option value="XML6">XML6 - Hồ sơ HIV/AIDS</option>
                                    <option value="XML7">XML7 - Giấy ra viện</option>
                                    <option value="XML8">XML8 - Tóm tắt hồ sơ bệnh án</option>
                                    <option value="XML9">XML9 - Giấy chuyển viện</option>
                                    <option value="XML10">XML10 - Giấy nghỉ dưỡng thai</option>
                                    <option value="XML11">XML11 - Giấy nghỉ việc BHXH</option>
                                    <option value="XML12">XML12 - Giám định y khoa</option>
                                    <option value="XML13">XML13 - Thông tin thuốc kháng sinh</option>
                                    <option value="XML14">XML14 - Thông tin phẫu thuật</option>
                                    <option value="XML15">XML15 - Quản lý điều trị lao</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="source-field">Trường nguồn</label>
                                <select class="form-control" id="source-field" name="source-field" required>
                                    <option value="">-- Chọn trường --</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Điều kiện kiểm tra</label>
                        <div class="card">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>Danh sách điều kiện</span>
                                    <button type="button" class="btn btn-success btn-sm" id="add-condition-btn">
                                        <i class="fas fa-plus"></i> Thêm điều kiện
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="conditions-container">
                                    <!-- Các điều kiện sẽ được thêm vào đây -->
                                </div>
                                <div id="no-conditions" class="text-muted text-center py-3">
                                    Chưa có điều kiện nào. Nhấn "Thêm điều kiện" để bắt đầu.
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group" id="custom-condition-container" style="display: none;">
                        <label for="custom-condition">Biểu thức JavaScript tùy chỉnh</label>
                        <textarea class="form-control" id="custom-condition" name="custom-condition" rows="3" placeholder="VD: sourceValue > targetValue && sourceValue < 1000"></textarea>
                        <small class="form-text text-muted">Sử dụng 'sourceValue' và 'targetValue' trong biểu thức. Trả về true/false.</small>
                    </div>

                    <div class="form-group">
                        <label for="error-message">Thông báo lỗi</label>
                        <input type="text" class="form-control" id="error-message" name="error-message" placeholder="Thông báo khi vi phạm quy tắc">
                    </div>

                    <div class="form-group">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="is-blocking" name="is-blocking">
                            <label class="form-check-label" for="is-blocking">
                                Chặn lưu dữ liệu khi vi phạm
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-primary" id="save-rule-btn">Lưu quy tắc</button>
            </div>
        </div>
    </div>
</div>

<!-- Template cho quy tắc kiểm tra mới -->
<template id="validation-rule-template">
    <div class="validation-rule" data-rule-id="{rule-id}">
        <div class="validation-rule-header">
            <h5>Quy tắc kiểm tra #<span class="rule-number">{rule-number}</span></h5>
            <div>
                <button type="button" class="btn btn-danger btn-sm delete-rule">
                    <i class="fas fa-trash"></i> Xóa
                </button>
            </div>
        </div>
        <div class="validation-rule-body">
            <div class="validation-rule-item">
                <div class="form-group">
                    <label>XML nguồn</label>
                    <select class="form-control source-xml-type">
                        <option value="">-- Chọn loại XML --</option>
                        <option value="XML0">XML0</option>
                        <option value="XML1">XML1</option>
                        <option value="XML2">XML2</option>
                        <option value="XML3">XML3</option>
                        <option value="XML4">XML4</option>
                        <option value="XML5">XML5</option>
                        <option value="XML6">XML6</option>
                        <option value="XML7">XML7</option>
                        <option value="XML8">XML8</option>
                        <option value="XML9">XML9</option>
                        <option value="XML10">XML10</option>
                        <option value="XML11">XML11</option>
                        <option value="XML12">XML12</option>
                        <option value="XML13">XML13</option>
                        <option value="XML14">XML14</option>
                        <option value="XML15">XML15</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Trường nguồn</label>
                    <select class="form-control source-field">
                        <option value="">-- Chọn trường --</option>
                    </select>
                </div>
            </div>
            <div class="validation-rule-item">
                <div class="form-group">
                    <label>Điều kiện</label>
                    <select class="form-control condition">
                        <option value="equal">Bằng (=)</option>
                        <option value="not_equal">Không bằng (!=)</option>
                        <option value="greater">Lớn hơn (>)</option>
                        <option value="greater_equal">Lớn hơn hoặc bằng (>=)</option>
                        <option value="less">Nhỏ hơn (<)</option>
                        <option value="less_equal">Nhỏ hơn hoặc bằng (<=)</option>
                        <option value="contains">Chứa</option>
                        <option value="not_contains">Không chứa</option>
                        <option value="starts_with">Bắt đầu bằng</option>
                        <option value="ends_with">Kết thúc bằng</option>
                        <option value="regex">Khớp biểu thức chính quy</option>
                        <option value="custom">Tùy chỉnh (JavaScript)</option>
                    </select>
                </div>
                <div class="form-group custom-condition-container" style="display: none;">
                    <label>Biểu thức JavaScript</label>
                    <textarea class="form-control custom-condition" rows="3" placeholder="return sourceValue > targetValue;"></textarea>
                    <small class="form-text text-muted">Sử dụng biến sourceValue và targetValue</small>
                </div>
            </div>
            <div class="validation-rule-item">
                <div class="form-group">
                    <label>XML đích</label>
                    <select class="form-control target-xml-type">
                        <option value="">-- Chọn loại XML --</option>
                        <option value="XML1">XML1</option>
                        <option value="XML2">XML2</option>
                        <option value="XML3">XML3</option>
                        <option value="XML4">XML4</option>
                        <option value="XML5">XML5</option>
                        <option value="XML6">XML6</option>
                        <option value="XML7">XML7</option>
                        <option value="XML8">XML8</option>
                        <option value="XML9">XML9</option>
                        <option value="XML10">XML10</option>
                        <option value="XML11">XML11</option>
                        <option value="XML12">XML12</option>
                        <option value="XML13">XML13</option>
                        <option value="XML14">XML14</option>
                        <option value="XML15">XML15</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Trường đích</label>
                    <select class="form-control target-field">
                        <option value="">-- Chọn trường --</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label>Thông báo lỗi</label>
            <input type="text" class="form-control error-message" placeholder="Nhập thông báo lỗi hiển thị khi quy tắc không thỏa mãn">
        </div>
        <div class="form-group">
            <div class="custom-control custom-checkbox">
                <input type="checkbox" class="custom-control-input is-blocking" id="blocking-{rule-id}">
                <label class="custom-control-label" for="blocking-{rule-id}">Chặn lưu dữ liệu khi không thỏa mãn</label>
            </div>
        </div>
    </div>
</template>
{% endblock %}

{% block extra_js %}
<script src="{% static "tabulator/dist/js/tabulator.min.js" %}"></script>
<script>
    $(function() {
        // Dữ liệu cho Tabulator với grouping
        let allFieldsData = [];

        // Hàm tải tất cả trường từ tất cả XML types
        function loadAllXmlFields() {
            const xmlTypes = ['XML0', 'XML1', 'XML2', 'XML3', 'XML4', 'XML5', 'XML6', 'XML7', 'XML8', 'XML9', 'XML10', 'XML11', 'XML12', 'XML13', 'XML14', 'XML15'];
            const xmlDescriptions = {
                'XML0': 'Thông tin checkin',
                'XML1': 'Thông tin tổng hợp',
                'XML2': 'Chi phí khám chữa bệnh',
                'XML3': 'Thông tin thuốc',
                'XML4': 'Dịch vụ cận lâm sàng',
                'XML5': 'Diễn biến bệnh',
                'XML6': 'Hồ sơ HIV/AIDS',
                'XML7': 'Giấy ra viện',
                'XML8': 'Tóm tắt hồ sơ bệnh án',
                'XML9': 'Giấy chuyển viện',
                'XML10': 'Giấy nghỉ dưỡng thai',
                'XML11': 'Giấy nghỉ việc BHXH',
                'XML12': 'Giám định y khoa',
                'XML13': 'Thông tin thuốc kháng sinh',
                'XML14': 'Thông tin phẫu thuật',
                'XML15': 'Quản lý điều trị lao'
            };

            allFieldsData = [];

            // Tải từng XML type
            const promises = xmlTypes.map(xmlType => {
                return new Promise((resolve) => {
                    $.ajax({
                        url: "{% url 'xml4750:get_xml_fields' %}",
                        type: "GET",
                        data: { xml_type: xmlType },
                        success: function(response) {
                            if (response.success && response.fields.length > 0) {
                                response.fields.forEach(field => {
                                    allFieldsData.push({
                                        xmlType: xmlType,
                                        xmlDescription: xmlDescriptions[xmlType],
                                        xmlGroup: `${xmlType} - ${xmlDescriptions[xmlType]}`,
                                        field: field.name,
                                        description: field.description,
                                        dataType: getDefaultDataType(field.name),
                                        compareType: getDefaultCompareType(field.name),
                                        sizeValue: getDefaultSizeValue(field.name),
                                        required: isRequiredField(xmlType, field.name),
                                        warning: false,
                                        blocking: false
                                    });
                                });
                            }
                            resolve();
                        },
                        error: function() {
                            console.warn(`Could not load fields for ${xmlType}`);
                            resolve();
                        }
                    });
                });
            });

            return Promise.all(promises);
        }

        // Hàm xác định kiểu dữ liệu mặc định dựa trên tên trường
        function getDefaultDataType(fieldName) {
            const lowerField = fieldName.toLowerCase();

            if (lowerField.includes('ngay') || lowerField.includes('date')) {
                return 'date';
            }
            if (lowerField.includes('gio') || lowerField.includes('time')) {
                return 'datetime';
            }
            if (lowerField.includes('gia') || lowerField.includes('tien') ||
                lowerField.includes('luong') || lowerField.includes('so')) {
                return 'number';
            }
            if (lowerField.includes('check') || lowerField.includes('flag')) {
                return 'boolean';
            }

            return 'string';
        }

        // Hàm xác định trường bắt buộc dựa trên tên trường và XML type
        function isRequiredField(xmlType, fieldName) {
            const requiredFields = {
                'XML0': ['maLK', 'maCSKCB'],
                'XML1': ['maLK', 'maBN', 'hoTen', 'ngaySinh', 'gioiTinh'],
                'XML2': ['maLK', 'maDV', 'soLuong', 'donGia'],
                'XML3': ['maLK', 'maThuoc', 'soLuong', 'donGia']
            };

            const required = requiredFields[xmlType] || [];
            return required.includes(fieldName);
        }

        // Hàm xác định kiểu so sánh mặc định
        function getDefaultCompareType(fieldName) {
            const lowerField = fieldName.toLowerCase();

            if (lowerField.includes('ma') && (lowerField.includes('lk') || lowerField.includes('bn') || lowerField.includes('cskcb'))) {
                return 'pattern'; // Mã thường có pattern cụ thể
            }
            if (lowerField.includes('ngay') || lowerField.includes('date')) {
                return 'pattern'; // Ngày có format cụ thể
            }
            if (lowerField.includes('gia') || lowerField.includes('tien') || lowerField.includes('luong')) {
                return 'range'; // Giá tiền thường có khoảng
            }
            if (lowerField.includes('ten') || lowerField.includes('name')) {
                return 'length'; // Tên có độ dài
            }
            if (lowerField.includes('so') && !lowerField.includes('luong')) {
                return 'range'; // Số lượng có khoảng
            }

            return 'exact'; // Mặc định là chính xác
        }

        // Hàm xác định kích thước/giá trị mặc định
        function getDefaultSizeValue(fieldName) {
            const lowerField = fieldName.toLowerCase();

            if (lowerField.includes('ma') && lowerField.includes('lk')) {
                return '^[A-Z0-9]{10,20}$'; // Pattern cho mã liên kết
            }
            if (lowerField.includes('ma') && lowerField.includes('bn')) {
                return '^[A-Z0-9]{5,15}$'; // Pattern cho mã bệnh nhân
            }
            if (lowerField.includes('ngay')) {
                return '^[0-9]{8}$|^[0-9]{12}$'; // YYYYMMDD hoặc YYYYMMDDHHMM
            }
            if (lowerField.includes('gia') || lowerField.includes('tien')) {
                return '0-999999999'; // Khoảng giá tiền
            }
            if (lowerField.includes('ten') || lowerField.includes('name')) {
                return '2-255'; // Độ dài tên
            }
            if (lowerField.includes('so') && lowerField.includes('luong')) {
                return '0-9999'; // Số lượng
            }

            return ''; // Không có giới hạn mặc định
        }

        // Khởi tạo Tabulator cho cấu hình trường với grouping
        let fieldConfigTable = new Tabulator("#field-config-table", {
            layout: "fitColumns",
            height: 600,
            placeholder: "Nhấn 'Tải tất cả trường' để hiển thị cấu hình",
            groupBy: "xmlGroup",
            groupStartOpen: false,
            groupHeader: function(value, count, data, group) {
                return `<i class="fas fa-file-code"></i> ${value} <span class="badge badge-secondary ml-2">${count} trường</span>`;
            },
            columns: [
                {title: "Trường", field: "field", width: 150, headerFilter: true},
                {title: "Mô tả", field: "description", width: 200},
                {title: "Kiểu dữ liệu DB", field: "dataType", width: 120, editor: "select", editorParams: {
                    values: {
                        "string": "Chuỗi",
                        "number": "Số",
                        "date": "Ngày",
                        "datetime": "Ngày giờ",
                        "boolean": "Logic"
                    }
                }, formatter: function(cell) {
                    const value = cell.getValue();
                    const valueMap = {
                        "string": "Chuỗi",
                        "number": "Số",
                        "date": "Ngày",
                        "datetime": "Ngày giờ",
                        "boolean": "Logic"
                    };
                    return valueMap[value] || value;
                }},
                {
                    title: "Kiểu so sánh",
                    field: "compareType",
                    width: 120,
                    editor: "select",
                    editorParams: {
                        values: {
                            "exact": "Chính xác",
                            "length": "Độ dài",
                            "range": "Khoảng giá trị",
                            "pattern": "Mẫu regex",
                            "list": "Danh sách cho phép",
                            "skip": "Bỏ qua kiểm tra"
                        },
                        clearable: false,
                        searchable: false
                    },
                    formatter: function(cell) {
                        const value = cell.getValue();
                        const valueMap = {
                            "exact": "Chính xác",
                            "length": "Độ dài",
                            "range": "Khoảng giá trị",
                            "pattern": "Mẫu regex",
                            "list": "Danh sách cho phép",
                            "skip": "Bỏ qua kiểm tra"
                        };
                        return valueMap[value] || value;
                    }
                },
                {title: "Kích thước/Giá trị", field: "sizeValue", width: 150, editor: "input",
                 editorParams: {
                     placeholder: "VD: 255 (số), 1-100 (khoảng), ^[0-9]+$ (regex)"
                 },
                 cellEdited: function(cell) {
                     const row = cell.getRow();
                     const compareType = row.getData().compareType;
                     const value = cell.getValue();

                     // Validate dựa trên kiểu so sánh
                     if (compareType === 'length' && value && !/^\d+$/.test(value)) {
                         Swal.fire('Cảnh báo', 'Kiểu độ dài cần nhập số nguyên dương', 'warning');
                         cell.setValue('');
                     } else if (compareType === 'range' && value && !/^\d+-\d+$/.test(value)) {
                         Swal.fire('Cảnh báo', 'Kiểu khoảng giá trị cần định dạng: số-số (VD: 1-100)', 'warning');
                         cell.setValue('');
                     }
                 }},
                {title: "Bắt buộc", field: "required", width: 80, editor: "tickCross", formatter: "tickCross", hozAlign: "center"},
                {title: "Cảnh báo", field: "warning", width: 80, editor: "tickCross", formatter: "tickCross", hozAlign: "center"},
                {title: "Chặn lưu", field: "blocking", width: 80, editor: "tickCross", formatter: "tickCross", hozAlign: "center"}
            ],
            cellEdited: function(cell) {
                // Đánh dấu dữ liệu đã thay đổi
                const row = cell.getRow();
                row.getElement().style.backgroundColor = "#fff3cd";
            }
        });

        // Khởi tạo Tabulator cho cấu hình nâng cao
        let advancedRulesTable = new Tabulator("#validation-rules-table", {
            layout: "fitColumns",
            height: 400,
            placeholder: "Chưa có quy tắc kiểm tra nào. Nhấn 'Thêm quy tắc' để bắt đầu.",
            columns: [
                {title: "XML nguồn", field: "sourceXmlType", width: 100, headerFilter: true},
                {title: "Trường nguồn", field: "sourceField", width: 120, headerFilter: true},
                {title: "Điều kiện", field: "condition", width: 120, headerFilter: true, formatter: function(cell) {
                    const conditionMap = {
                        'equals': 'Bằng',
                        'not_equals': 'Không bằng',
                        'greater_than': 'Lớn hơn',
                        'less_than': 'Nhỏ hơn',
                        'greater_equal': 'Lớn hơn hoặc bằng',
                        'less_equal': 'Nhỏ hơn hoặc bằng',
                        'contains': 'Chứa',
                        'not_contains': 'Không chứa',
                        'starts_with': 'Bắt đầu bằng',
                        'ends_with': 'Kết thúc bằng',
                        'regex': 'Regex',
                        'in_list': 'Trong danh sách',
                        'not_in_list': 'Không trong danh sách',
                        'custom': 'Tùy chỉnh'
                    };
                    return conditionMap[cell.getValue()] || cell.getValue();
                }},
                {title: "Biểu thức so sánh", field: "compareExpression", width: 200, headerFilter: true, formatter: function(cell) {
                    const value = cell.getValue();
                    if (!value) return '';
                    // Hiển thị tối đa 50 ký tự, thêm ... nếu dài hơn
                    return value.length > 50 ? value.substring(0, 50) + '...' : value;
                }},
                {title: "Thông báo lỗi", field: "errorMessage", width: 200},
                {title: "Chặn lưu", field: "isBlocking", width: 80, formatter: "tickCross", hozAlign: "center"},
                {title: "Hành động", field: "actions", formatter: function(cell, formatterParams, onRendered) {
                    return '<button class="btn btn-sm btn-primary edit-rule" title="Sửa"><i class="fas fa-edit"></i></button> ' +
                           '<button class="btn btn-sm btn-danger delete-rule ml-1" title="Xóa"><i class="fas fa-trash"></i></button>';
                }, width: 100, headerSort: false, hozAlign: "center"}
            ],
            rowClick: function(e, row) {
                // Ngăn không cho row click khi nhấn vào button
                if (e.target.tagName === 'BUTTON' || e.target.tagName === 'I') {
                    return;
                }
            }
        });

        // Xử lý sự kiện tải tất cả trường
        $('#load-all-fields').click(function() {
            const btn = $(this);
            btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Đang tải...');

            loadAllXmlFields().then(() => {
                // Tải cấu hình hiện có từ server
                loadExistingConfigs().then(() => {
                    // Cập nhật dữ liệu vào bảng
                    fieldConfigTable.setData(allFieldsData);
                    btn.prop('disabled', false).html('<i class="fas fa-sync"></i> Tải tất cả trường');

                    Swal.fire('Thành công', `Đã tải ${allFieldsData.length} trường từ ${new Set(allFieldsData.map(f => f.xmlType)).size} loại XML`, 'success');
                });
            }).catch(error => {
                console.error('Error loading fields:', error);
                btn.prop('disabled', false).html('<i class="fas fa-sync"></i> Tải tất cả trường');
                Swal.fire('Lỗi', 'Không thể tải dữ liệu trường', 'error');
            });
        });

        // Hàm tải cấu hình hiện có từ server
        function loadExistingConfigs() {
            return new Promise((resolve) => {
                $.ajax({
                    url: "{% url 'xml4750:get_all_field_configs' %}",
                    type: "GET",
                    success: function(response) {
                        if (response.success) {
                            // Cập nhật cấu hình hiện có vào dữ liệu
                            allFieldsData.forEach(field => {
                                const configKey = `${field.xmlType}.${field.field}`;
                                const config = response.configs[configKey];
                                if (config) {
                                    field.dataType = config.dataType || field.dataType;
                                    field.required = config.required || field.required;
                                    field.format = config.format || field.format;
                                    field.blocking = config.blocking !== undefined ? config.blocking : field.blocking;
                                }
                            });
                        }
                        resolve();
                    },
                    error: function() {
                        console.warn('Could not load existing configs');
                        resolve();
                    }
                });
            });
        }

        // Hàm tải cấu hình cho một trường cụ thể
        function loadFieldConfig(xmlType, fieldName) {
            // Trong thực tế, bạn sẽ tải dữ liệu từ server
            // Ở đây chúng ta sẽ mô phỏng bằng cách tạo dữ liệu mẫu
            $.ajax({
                url: "{% url 'xml4750:get_field_config' %}",
                type: "GET",
                data: {
                    xml_type: xmlType,
                    field_name: fieldName
                },
                success: function(response) {
                    if (response.success) {
                        fieldConfigTable.setData([response.config]);
                    } else {
                        // Nếu chưa có cấu hình, tạo một cấu hình mặc định
                        const defaultConfig = {
                            field: fieldName,
                            xmlType: xmlType,
                            dataType: getDefaultDataType(fieldName),
                            required: isRequiredField(xmlType, fieldName),
                            format: "",
                            defaultValue: "",
                            description: "",
                            warning: false,
                            blocking: false
                        };
                        fieldConfigTable.setData([defaultConfig]);
                    }
                },
                error: function(xhr, status, error) {
                    console.error("Error loading field config:", error);
                    Swal.fire('Lỗi', 'Không thể tải cấu hình trường', 'error');
                }
            });
        }

        // Hàm tải cấu hình cho một loại XML
        function loadXmlTypeConfig(xmlType) {
            $.ajax({
                url: "{% url 'xml4750:get_xml_type_config' %}",
                type: "GET",
                data: {
                    xml_type: xmlType
                },
                success: function(response) {
                    if (response.success) {
                        fieldConfigTable.setData(response.configs);
                    } else {
                        // Nếu chưa có cấu hình, hiển thị thông báo
                        fieldConfigTable.setData([]);
                        Swal.fire('Thông báo', 'Chưa có cấu hình cho loại XML này', 'info');
                    }
                },
                error: function(xhr, status, error) {
                    console.error("Error loading XML type config:", error);
                    Swal.fire('Lỗi', 'Không thể tải cấu hình loại XML', 'error');
                }
            });
        }

        // Hàm xác định kiểu dữ liệu mặc định dựa trên tên trường
        function getDefaultDataType(fieldName) {
            if (fieldName.includes('ngay') || fieldName.includes('Ngay')) {
                return "date";
            } else if (fieldName.includes('so') || fieldName.includes('So') || 
                       fieldName.includes('tien') || fieldName.includes('Tien') ||
                       fieldName.includes('gia') || fieldName.includes('Gia')) {
                return "number";
            } else {
                return "string";
            }
        }

        // Hàm xác định trường bắt buộc
        function isRequiredField(xmlType, fieldName) {
            // Danh sách các trường bắt buộc theo loại XML
            const requiredFields = {
                'XML0': ['maLK', 'maCSKCB'],
                'XML1': ['maLK', 'hoTen', 'ngaySinh', 'maTheBHYT'],
                'XML2': ['maLK', 'maDV', 'soLuong', 'donGia'],
                'XML3': ['maLK', 'maThuoc', 'soLuong', 'donGia']
                // Thêm các loại XML khác
            };
            
            return requiredFields[xmlType] && requiredFields[xmlType].includes(fieldName);
        }

        // Lưu cấu hình cơ bản
        $('#save-basic-config').click(function() {
            const configData = fieldConfigTable.getData();

            if (configData.length === 0) {
                Swal.fire('Cảnh báo', 'Không có dữ liệu cấu hình để lưu', 'warning');
                return;
            }

            const btn = $(this);
            btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Đang lưu...');

            $.ajax({
                url: "{% url 'xml4750:save_field_config' %}",
                type: "POST",
                data: {
                    'csrfmiddlewaretoken': '{{ csrf_token }}',
                    'configs': JSON.stringify(configData)
                },
                success: function(response) {
                    btn.prop('disabled', false).html('<i class="fas fa-save"></i> Lưu cấu hình');

                    if (response.success) {
                        // Reset background color của các dòng đã chỉnh sửa
                        fieldConfigTable.getRows().forEach(row => {
                            row.getElement().style.backgroundColor = "";
                        });

                        Swal.fire('Thành công', `Đã lưu cấu hình cho ${configData.length} trường`, 'success');
                    } else {
                        Swal.fire('Lỗi', response.message || 'Không thể lưu cấu hình', 'error');
                    }
                },
                error: function(xhr, status, error) {
                    btn.prop('disabled', false).html('<i class="fas fa-save"></i> Lưu cấu hình');
                    console.error("Error saving config:", error);
                    Swal.fire('Lỗi', 'Không thể lưu cấu hình', 'error');
                }
            });
        });

        // Biến đếm số quy tắc
        let ruleCounter = 0;

        // Thêm quy tắc kiểm tra mới
        $('#add-validation-rule').click(function() {
            openValidationRuleModal();
        });

        // Mở modal thêm/sửa quy tắc
        function openValidationRuleModal(ruleData = null) {
            const modal = $('#validation-rule-modal');
            const form = $('#validation-rule-form');

            // Reset form
            form[0].reset();
            $('#custom-condition-container').hide();

            if (ruleData) {
                // Chế độ sửa
                $('#validation-rule-modal-label').text('Sửa quy tắc kiểm tra');
                $('#rule-id').val(ruleData.id);
                $('#source-xml-type').val(ruleData.sourceXmlType);
                $('#condition').val(ruleData.condition);
                $('#compare-expression').val(ruleData.compareExpression);
                $('#error-message').val(ruleData.errorMessage);
                $('#is-blocking').prop('checked', ruleData.isBlocking);

                if (ruleData.condition === 'custom') {
                    $('#custom-condition-container').show();
                    $('#custom-condition').val(ruleData.customCondition);
                }

                // Tải trường cho XML nguồn
                if (ruleData.sourceXmlType) {
                    loadFieldsForXmlType(ruleData.sourceXmlType, $('#source-field'), function() {
                        $('#source-field').val(ruleData.sourceField);
                    });
                }
            } else {
                // Chế độ thêm mới
                $('#validation-rule-modal-label').text('Thêm quy tắc kiểm tra');
                $('#rule-id').val('');
            }

            modal.modal('show');
        }

        // Xử lý sự kiện trong modal
        $('#source-xml-type').change(function() {
            const xmlType = $(this).val();
            const sourceFieldSelect = $('#source-field');

            if (xmlType) {
                loadFieldsForXmlType(xmlType, sourceFieldSelect);
            } else {
                sourceFieldSelect.html('<option value="">-- Chọn trường --</option>');
            }
        });



        // Biến đếm điều kiện
        let conditionCounter = 0;

        // Xử lý thêm điều kiện
        $('#add-condition-btn').click(function() {
            addCondition();
        });

        function addCondition(data = null) {
            conditionCounter++;
            const conditionId = `condition-${conditionCounter}`;

            const conditionHtml = `
                <div class="condition-item border rounded p-3 mb-3" data-condition-id="${conditionId}">
                    <div class="row">
                        <div class="col-md-3">
                            <label>XML điều kiện</label>
                            <select class="form-control xml-condition-select" name="xml_condition_${conditionId}">
                                <option value="">-- Chọn XML --</option>
                                <option value="XML0">XML0 - Thông tin hành chính</option>
                                <option value="XML1">XML1 - Thông tin tổng hợp</option>
                                <option value="XML2">XML2 - Chi phí khám chữa bệnh</option>
                                <option value="XML3">XML3 - Thông tin thuốc</option>
                                <option value="XML4">XML4 - Thông tin DVKT</option>
                                <option value="XML5">XML5 - Thông tin VTYT</option>
                                <option value="XML6">XML6</option>
                                <option value="XML7">XML7</option>
                                <option value="XML8">XML8</option>
                                <option value="XML9">XML9</option>
                                <option value="XML10">XML10</option>
                                <option value="XML11">XML11</option>
                                <option value="XML12">XML12</option>
                                <option value="XML13">XML13</option>
                                <option value="XML14">XML14</option>
                                <option value="XML15">XML15</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label>Trường điều kiện</label>
                            <select class="form-control field-condition-select" name="field_condition_${conditionId}">
                                <option value="">-- Chọn trường --</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label>Toán tử</label>
                            <select class="form-control operator-select" name="operator_${conditionId}">
                                <option value="equals">Bằng</option>
                                <option value="not_equals">Không bằng</option>
                                <option value="greater_than">Lớn hơn</option>
                                <option value="less_than">Nhỏ hơn</option>
                                <option value="greater_equal">Lớn hơn hoặc bằng</option>
                                <option value="less_equal">Nhỏ hơn hoặc bằng</option>
                                <option value="contains">Chứa</option>
                                <option value="not_contains">Không chứa</option>
                                <option value="starts_with">Bắt đầu bằng</option>
                                <option value="ends_with">Kết thúc bằng</option>
                                <option value="regex">Regex</option>
                                <option value="in_list">Trong danh sách</option>
                                <option value="not_in_list">Không trong danh sách</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label>Giá trị so sánh <small class="text-muted">(tùy chọn)</small></label>
                            <input type="text" class="form-control value-input" name="value_${conditionId}" placeholder="Để trống nếu so sánh giữa các cột">
                        </div>
                        <div class="col-md-1">
                            <label>&nbsp;</label>
                            <button type="button" class="btn btn-danger btn-sm d-block remove-condition-btn">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;

            $('#conditions-container').append(conditionHtml);
            $('#no-conditions').hide();

            // Xử lý thay đổi XML để load trường
            $(`.condition-item[data-condition-id="${conditionId}"] .xml-condition-select`).change(function() {
                const xmlType = $(this).val();
                const fieldSelect = $(this).closest('.condition-item').find('.field-condition-select');
                loadFieldsForXML(xmlType, fieldSelect);
            });

            // Xử lý xóa điều kiện
            $(`.condition-item[data-condition-id="${conditionId}"] .remove-condition-btn`).click(function() {
                $(this).closest('.condition-item').remove();
                if ($('#conditions-container .condition-item').length === 0) {
                    $('#no-conditions').show();
                }
            });

            // Nếu có data, điền vào form
            if (data) {
                const conditionItem = $(`.condition-item[data-condition-id="${conditionId}"]`);
                conditionItem.find('.xml-condition-select').val(data.xml_type);
                conditionItem.find('.operator-select').val(data.operator);
                conditionItem.find('.value-input').val(data.value);

                // Load fields và chọn field
                if (data.xml_type) {
                    loadFieldsForXML(data.xml_type, conditionItem.find('.field-condition-select'), data.field);
                }
            }
        }

        function loadFieldsForXML(xmlType, fieldSelect, selectedField = null) {
            if (!xmlType) {
                fieldSelect.html('<option value="">-- Chọn trường --</option>');
                return;
            }

            // Lấy danh sách trường từ cấu hình XML
            const fields = getFieldsForXMLType(xmlType);
            let options = '<option value="">-- Chọn trường --</option>';

            fields.forEach(field => {
                options += `<option value="${field.name}">${field.description}</option>`;
            });

            fieldSelect.html(options);

            if (selectedField) {
                fieldSelect.val(selectedField);
            }
        }

        function getFieldsForXMLType(xmlType) {
            // Trả về danh sách trường cho từng loại XML
            const xmlFields = {
                'XML0': [
                    {name: 'maLK', description: 'Mã liên kết'},
                    {name: 'stt', description: 'Số thứ tự'},
                    {name: 'maCSKCB', description: 'Mã CSKCB'},
                    {name: 'tenCSKCB', description: 'Tên CSKCB'}
                ],
                'XML1': [
                    {name: 'maLK', description: 'Mã liên kết'},
                    {name: 'maBN', description: 'Mã bệnh nhân'},
                    {name: 'hoTen', description: 'Họ và tên'},
                    {name: 'ngaySinh', description: 'Ngày sinh'},
                    {name: 'gioiTinh', description: 'Giới tính'},
                    {name: 'diaChi', description: 'Địa chỉ'},
                    {name: 'maBHXH', description: 'Mã BHXH'},
                    {name: 'gtTheBHYT', description: 'Giá trị thẻ BHYT'},
                    {name: 'maKV', description: 'Mã khu vực'},
                    {name: 'ngayVao', description: 'Ngày vào'},
                    {name: 'ngayRa', description: 'Ngày ra'},
                    {name: 'soNgayDieuTri', description: 'Số ngày điều trị'},
                    {name: 'ketQuaDieuTri', description: 'Kết quả điều trị'},
                    {name: 'tinhTrangRaVien', description: 'Tình trạng ra viện'},
                    {name: 'ngayTToan', description: 'Ngày thanh toán'},
                    {name: 'mucHuong', description: 'Mức hưởng'},
                    {name: 'tTien', description: 'Tổng tiền'},
                    {name: 'tNguonKhac', description: 'Tiền nguồn khác'},
                    {name: 'tBNTT', description: 'Tiền BN thanh toán'},
                    {name: 'tBHYT', description: 'Tiền BHYT'},
                    {name: 'tBNCT', description: 'Tiền BN cùng chi trả'},
                    {name: 'tNgoaiDM', description: 'Tiền ngoài danh mục'},
                    {name: 'namQT', description: 'Năm quyết toán'},
                    {name: 'thangQT', description: 'Tháng quyết toán'}
                ],
                'XML2': [
                    {name: 'maLK', description: 'Mã liên kết'},
                    {name: 'stt', description: 'Số thứ tự'},
                    {name: 'maDichVu', description: 'Mã dịch vụ'},
                    {name: 'maNhom', description: 'Mã nhóm'},
                    {name: 'tenDichVu', description: 'Tên dịch vụ'},
                    {name: 'donViTinh', description: 'Đơn vị tính'},
                    {name: 'soLuong', description: 'Số lượng'},
                    {name: 'donGia', description: 'Đơn giá'},
                    {name: 'thanhTien', description: 'Thành tiền'},
                    {name: 'mucHuong', description: 'Mức hưởng'},
                    {name: 'tNguonKhac', description: 'Tiền nguồn khác'},
                    {name: 'tBNTT', description: 'Tiền BN thanh toán'},
                    {name: 'tBHYT', description: 'Tiền BHYT'},
                    {name: 'tBNCT', description: 'Tiền BN cùng chi trả'},
                    {name: 'tNgoaiDM', description: 'Tiền ngoài danh mục'},
                    {name: 'maPTTT', description: 'Mã phương thức thanh toán'}
                ],
                'XML3': [
                    {name: 'maLK', description: 'Mã liên kết'},
                    {name: 'stt', description: 'Số thứ tự'},
                    {name: 'maThuoc', description: 'Mã thuốc'},
                    {name: 'maNhom', description: 'Mã nhóm'},
                    {name: 'tenThuoc', description: 'Tên thuốc'},
                    {name: 'donViTinh', description: 'Đơn vị tính'},
                    {name: 'hamLuong', description: 'Hàm lượng'},
                    {name: 'duongDung', description: 'Đường dùng'},
                    {name: 'lieuDung', description: 'Liều dùng'},
                    {name: 'soLuong', description: 'Số lượng'},
                    {name: 'donGia', description: 'Đơn giá'},
                    {name: 'thanhTien', description: 'Thành tiền'},
                    {name: 'mucHuong', description: 'Mức hưởng'},
                    {name: 'tNguonKhac', description: 'Tiền nguồn khác'},
                    {name: 'tBNTT', description: 'Tiền BN thanh toán'},
                    {name: 'tBHYT', description: 'Tiền BHYT'},
                    {name: 'tBNCT', description: 'Tiền BN cùng chi trả'},
                    {name: 'tNgoaiDM', description: 'Tiền ngoài danh mục'},
                    {name: 'maPTTT', description: 'Mã phương thức thanh toán'}
                ],
                'XML4': [
                    {name: 'maLK', description: 'Mã liên kết'},
                    {name: 'stt', description: 'Số thứ tự'},
                    {name: 'maDichVu', description: 'Mã dịch vụ'},
                    {name: 'maNhom', description: 'Mã nhóm'},
                    {name: 'tenDichVu', description: 'Tên dịch vụ'},
                    {name: 'donViTinh', description: 'Đơn vị tính'},
                    {name: 'soLuong', description: 'Số lượng'},
                    {name: 'donGia', description: 'Đơn giá'},
                    {name: 'thanhTien', description: 'Thành tiền'},
                    {name: 'mucHuong', description: 'Mức hưởng'},
                    {name: 'tNguonKhac', description: 'Tiền nguồn khác'},
                    {name: 'tBNTT', description: 'Tiền BN thanh toán'},
                    {name: 'tBHYT', description: 'Tiền BHYT'},
                    {name: 'tBNCT', description: 'Tiền BN cùng chi trả'},
                    {name: 'tNgoaiDM', description: 'Tiền ngoài danh mục'},
                    {name: 'maPTTT', description: 'Mã phương thức thanh toán'}
                ],
                'XML5': [
                    {name: 'maLK', description: 'Mã liên kết'},
                    {name: 'stt', description: 'Số thứ tự'},
                    {name: 'maDichVu', description: 'Mã dịch vụ'},
                    {name: 'maNhom', description: 'Mã nhóm'},
                    {name: 'tenDichVu', description: 'Tên dịch vụ'},
                    {name: 'donViTinh', description: 'Đơn vị tính'},
                    {name: 'soLuong', description: 'Số lượng'},
                    {name: 'donGia', description: 'Đơn giá'},
                    {name: 'thanhTien', description: 'Thành tiền'},
                    {name: 'mucHuong', description: 'Mức hưởng'},
                    {name: 'tNguonKhac', description: 'Tiền nguồn khác'},
                    {name: 'tBNTT', description: 'Tiền BN thanh toán'},
                    {name: 'tBHYT', description: 'Tiền BHYT'},
                    {name: 'tBNCT', description: 'Tiền BN cùng chi trả'},
                    {name: 'tNgoaiDM', description: 'Tiền ngoài danh mục'},
                    {name: 'maPTTT', description: 'Mã phương thức thanh toán'}
                ],
                // Các XML khác có thể có cấu trúc tương tự hoặc khác
                'XML6': [
                    {name: 'maLK', description: 'Mã liên kết'},
                    {name: 'stt', description: 'Số thứ tự'}
                ],
                'XML7': [
                    {name: 'maLK', description: 'Mã liên kết'},
                    {name: 'stt', description: 'Số thứ tự'}
                ],
                'XML8': [
                    {name: 'maLK', description: 'Mã liên kết'},
                    {name: 'stt', description: 'Số thứ tự'}
                ],
                'XML9': [
                    {name: 'maLK', description: 'Mã liên kết'},
                    {name: 'stt', description: 'Số thứ tự'}
                ],
                'XML10': [
                    {name: 'maLK', description: 'Mã liên kết'},
                    {name: 'stt', description: 'Số thứ tự'}
                ],
                'XML11': [
                    {name: 'maLK', description: 'Mã liên kết'},
                    {name: 'stt', description: 'Số thứ tự'}
                ],
                'XML12': [
                    {name: 'maLK', description: 'Mã liên kết'},
                    {name: 'stt', description: 'Số thứ tự'}
                ],
                'XML13': [
                    {name: 'maLK', description: 'Mã liên kết'},
                    {name: 'stt', description: 'Số thứ tự'}
                ],
                'XML14': [
                    {name: 'maLK', description: 'Mã liên kết'},
                    {name: 'stt', description: 'Số thứ tự'}
                ],
                'XML15': [
                    {name: 'maLK', description: 'Mã liên kết'},
                    {name: 'stt', description: 'Số thứ tự'}
                ]
            };

            return xmlFields[xmlType] || [];
        }

        // Lưu quy tắc từ modal
        $('#save-rule-btn').click(function() {
            const form = $('#validation-rule-form');
            const formData = new FormData(form[0]);

            // Validate form
            const sourceXmlType = formData.get('source-xml-type');
            const sourceField = formData.get('source-field');
            const condition = formData.get('condition');
            const compareExpression = formData.get('compare-expression');

            if (!sourceXmlType || !sourceField || !condition) {
                Swal.fire('Cảnh báo', 'Vui lòng điền đầy đủ thông tin bắt buộc', 'warning');
                return;
            }

            if (condition === 'custom' && !formData.get('custom-condition')) {
                Swal.fire('Cảnh báo', 'Vui lòng nhập biểu thức JavaScript cho điều kiện tùy chỉnh', 'warning');
                return;
            }

            if (!compareExpression && condition !== 'custom') {
                Swal.fire('Cảnh báo', 'Vui lòng nhập biểu thức so sánh', 'warning');
                return;
            }

            const btn = $(this);
            btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Đang lưu...');

            // Tạo object quy tắc
            const ruleData = {
                id: formData.get('rule-id') || null,
                sourceXmlType: sourceXmlType,
                sourceField: sourceField,
                condition: condition,
                compareExpression: compareExpression || '',
                customCondition: formData.get('custom-condition') || '',
                errorMessage: formData.get('error-message') || `${sourceXmlType}.${sourceField} vi phạm quy tắc ${condition}`,
                isBlocking: formData.has('is-blocking')
            };

            // Lưu vào database ngay lập tức
            $.ajax({
                url: "{% url 'xml4750:save_single_validation_rule' %}",
                type: "POST",
                data: {
                    'csrfmiddlewaretoken': '{{ csrf_token }}',
                    'rule': JSON.stringify(ruleData)
                },
                success: function(response) {
                    btn.prop('disabled', false).html('Lưu quy tắc');

                    if (response.success) {
                        // Cập nhật ID từ database
                        ruleData.id = response.rule_id;

                        // Thêm hoặc cập nhật vào bảng
                        const existingData = advancedRulesTable.getData();
                        const existingIndex = existingData.findIndex(rule => rule.id === ruleData.id);

                        if (existingIndex >= 0) {
                            // Cập nhật quy tắc hiện có
                            advancedRulesTable.updateRow(existingIndex, ruleData);
                        } else {
                            // Thêm quy tắc mới
                            advancedRulesTable.addRow(ruleData);
                        }

                        // Đóng modal
                        $('#validation-rule-modal').modal('hide');

                        Swal.fire('Thành công', 'Đã lưu quy tắc kiểm tra vào database', 'success');
                    } else {
                        Swal.fire('Lỗi', response.message || 'Không thể lưu quy tắc', 'error');
                    }
                },
                error: function(xhr, status, error) {
                    btn.prop('disabled', false).html('Lưu quy tắc');
                    console.error("Error saving rule:", error);
                    Swal.fire('Lỗi', 'Không thể lưu quy tắc kiểm tra', 'error');
                }
            });
        });

        // Xử lý sự kiện click trên bảng quy tắc
        $(document).on('click', '.edit-rule', function() {
            const button = $(this);
            const cell = button.closest('.tabulator-cell');
            const row = advancedRulesTable.getRow(cell.closest('.tabulator-row')[0]);
            const rowData = row.getData();

            openValidationRuleModal(rowData);
        });

        $(document).on('click', '.delete-rule', function() {
            const button = $(this);
            const cell = button.closest('.tabulator-cell');
            const row = advancedRulesTable.getRow(cell.closest('.tabulator-row')[0]);
            const rowData = row.getData();

            Swal.fire({
                title: 'Xác nhận xóa',
                text: `Bạn có chắc muốn xóa quy tắc "${rowData.sourceXmlType}.${rowData.sourceField}"?`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Xóa',
                cancelButtonText: 'Hủy'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Xóa từ database ngay lập tức
                    $.ajax({
                        url: "{% url 'xml4750:delete_validation_rule' %}",
                        type: "POST",
                        data: {
                            'csrfmiddlewaretoken': '{{ csrf_token }}',
                            'rule_id': rowData.id
                        },
                        success: function(response) {
                            if (response.success) {
                                row.delete();
                                Swal.fire('Đã xóa', 'Quy tắc đã được xóa khỏi database', 'success');
                            } else {
                                Swal.fire('Lỗi', response.message || 'Không thể xóa quy tắc', 'error');
                            }
                        },
                        error: function() {
                            Swal.fire('Lỗi', 'Không thể xóa quy tắc', 'error');
                        }
                    });
                }
            });
        });

        // Xóa quy tắc kiểm tra
        $(document).on('click', '.delete-rule', function() {
            const rule = $(this).closest('.validation-rule');
            
            Swal.fire({
                title: 'Xác nhận xóa',
                text: 'Bạn có chắc chắn muốn xóa quy tắc kiểm tra này?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Xóa',
                cancelButtonText: 'Hủy'
            }).then((result) => {
                if (result.isConfirmed) {
                    rule.remove();
                    updateRuleNumbers();
                }
            });
        });

        // Cập nhật số thứ tự của các quy tắc
        function updateRuleNumbers() {
            $('.validation-rule').each(function(index) {
                $(this).find('.rule-number').text(index + 1);
            });
        }

        // Thiết lập các dropdown trường khi chọn loại XML
        function setupXmlFieldSelectors(ruleId) {
            const rule = $(`.validation-rule[data-rule-id="${ruleId}"]`);

            if (rule.length === 0) {
                console.error('Rule element not found:', ruleId);
                return;
            }

            // Source XML Type change
            rule.find('.source-xml-type').off('change').on('change', function() {
                const xmlType = $(this).val();
                const sourceFieldSelect = rule.find('.source-field');

                if (xmlType) {
                    loadFieldsForXmlType(xmlType, sourceFieldSelect);
                } else {
                    sourceFieldSelect.html('<option value="">-- Chọn trường --</option>');
                }
            });

            // Target XML Type change
            rule.find('.target-xml-type').off('change').on('change', function() {
                const xmlType = $(this).val();
                const targetFieldSelect = rule.find('.target-field');

                if (xmlType) {
                    loadFieldsForXmlType(xmlType, targetFieldSelect);
                } else {
                    targetFieldSelect.html('<option value="">-- Chọn trường --</option>');
                }
            });
        }

        // Thiết lập hiển thị/ẩn điều kiện tùy chỉnh
        function setupCustomCondition(ruleId) {
            const rule = $(`.validation-rule[data-rule-id="${ruleId}"]`);

            if (rule.length === 0) {
                console.error('Rule element not found for custom condition:', ruleId);
                return;
            }

            rule.find('.condition').off('change').on('change', function() {
                const condition = $(this).val();
                const customContainer = rule.find('.custom-condition-container');

                if (condition === 'custom') {
                    customContainer.show();
                } else {
                    customContainer.hide();
                }
            });
        }

        // Tải danh sách trường cho loại XML
        function loadFieldsForXmlType(xmlType, selectElement, callback) {
            if (!xmlType || !selectElement || selectElement.length === 0) {
                console.error('Invalid parameters for loadFieldsForXmlType');
                return;
            }

            // Hiển thị loading
            selectElement.html('<option value="">-- Đang tải... --</option>');

            $.ajax({
                url: "{% url 'xml4750:get_xml_fields' %}",
                type: "GET",
                data: {
                    xml_type: xmlType
                },
                success: function(response) {
                    if (response.success && response.fields) {
                        let options = '<option value="">-- Chọn trường --</option>';
                        response.fields.forEach(field => {
                            const description = field.description || field.name;
                            options += `<option value="${field.name}">${field.name} (${description})</option>`;
                        });
                        selectElement.html(options);

                        if (typeof callback === 'function') {
                            callback();
                        }
                    } else {
                        selectElement.html('<option value="">-- Không có dữ liệu --</option>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error("Error loading fields for", xmlType, ":", error);
                    selectElement.html('<option value="">-- Lỗi tải dữ liệu --</option>');
                }
            });
        }

        // Lưu cấu hình nâng cao
        $('#save-advanced-config').click(function() {
            const rules = advancedRulesTable.getData();

            if (rules.length === 0) {
                Swal.fire('Cảnh báo', 'Không có quy tắc nào để lưu', 'warning');
                return;
            }

            const btn = $(this);
            btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Đang lưu...');

            $.ajax({
                url: "{% url 'xml4750:save_validation_rules' %}",
                type: "POST",
                data: {
                    'csrfmiddlewaretoken': '{{ csrf_token }}',
                    'rules': JSON.stringify(rules)
                },
                success: function(response) {
                    btn.prop('disabled', false).html('<i class="fas fa-save"></i> Lưu cấu hình');

                    if (response.success) {
                        Swal.fire('Thành công', `Đã lưu ${rules.length} quy tắc kiểm tra thành công`, 'success');
                    } else {
                        Swal.fire('Lỗi', response.message || 'Không thể lưu quy tắc kiểm tra', 'error');
                    }
                },
                error: function(xhr, status, error) {
                    btn.prop('disabled', false).html('<i class="fas fa-save"></i> Lưu cấu hình');
                    console.error("Error saving rules:", error);
                    Swal.fire('Lỗi', 'Không thể lưu quy tắc kiểm tra', 'error');
                }
            });
        });
            
            // Hàm lấy text mô tả điều kiện
            function getConditionText(condition) {
                const conditionTexts = {
                    'equal': 'bằng',
                    'not_equal': 'không bằng',
                    'greater': 'lớn hơn',
                    'greater_equal': 'lớn hơn hoặc bằng',
                    'less': 'nhỏ hơn',
                    'less_equal': 'nhỏ hơn hoặc bằng',
                    'contains': 'chứa',
                    'not_contains': 'không chứa',
                    'starts_with': 'bắt đầu bằng',
                    'ends_with': 'kết thúc bằng',
                    'regex': 'khớp với',
                    'custom': 'thỏa mãn điều kiện với'
                };
                
                return conditionTexts[condition] || condition;
            }
            
            // Tải các quy tắc đã lưu khi trang được tải
            function loadSavedRules() {
                $.ajax({
                    url: "{% url 'xml4750:get_validation_rules' %}",
                    type: "GET",
                    success: function(response) {
                        if (response.success && response.rules && response.rules.length > 0) {
                            // Tải dữ liệu vào bảng Tabulator
                            advancedRulesTable.setData(response.rules);
                            console.log(`Loaded ${response.rules.length} validation rules into table`);
                        } else {
                            console.log('No saved rules found');
                            // Để bảng trống
                            advancedRulesTable.setData([]);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error("Error loading saved rules:", error);
                        advancedRulesTable.setData([]);
                    }
                });
            }

            
            // Khởi tạo khi trang được tải
            loadSavedRules();

            // Tự động tải tất cả trường khi vào trang
            setTimeout(() => {
                $('#load-all-fields').trigger('click');
            }, 500);
        });
    </script>
    
    <!-- Thêm script cho validation -->
    <script src="{% static 'js/xml4750/validation_config.js' %}"></script>
    <script src="{% static 'js/xml4750/validation.js' %}"></script>
{% endblock %}