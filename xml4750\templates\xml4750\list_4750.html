{% extends 'layouts/base.html' %}
{% load static %}
{% load xml_filters %}

{% block title %}Quản lý dữ liệu XML theo Quyết định 4750{% endblock %}

{% block page_title %}Quản lý dữ liệu XML theo Quyết định 4750{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
<li class="breadcrumb-item active">Quản lý XML</li>
{% endblock %}


{% block extra_css %}
<link rel="stylesheet" href="{% static 'AdminLTE-3.0.1/plugins/sweetalert2-theme-bootstrap-4/bootstrap-4.min.css' %}">
<link href="{% static "tabulator/dist/css/tabulator.min.css" %}" rel="stylesheet">
<link href="{% static "tabulator/dist/css/tabulator_bootstrap4.min.css" %}" rel="stylesheet">
<link href="{% static 'css/custom_xml.css' %}" rel="stylesheet">
<link rel="stylesheet" href="{% static 'AdminLTE-3.0.1/plugins/select2/css/select2.min.css' %}">
<link rel="stylesheet" href="{% static 'AdminLTE-3.0.1/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css' %}">
<style>
/* Custom XML 4750 Styles */
.filter-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.nav-pills .nav-link {
    border-radius: 25px;
    transition: all 0.3s ease;
    font-weight: 500;
    border: 2px solid transparent;
}

.nav-pills .nav-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.nav-pills .nav-link.active {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-color: #007bff;
    box-shadow: 0 4px 15px rgba(0,123,255,0.3);
}

.text-purple { color: #6f42c1 !important; }
.text-pink { color: #e83e8c !important; }
.text-brown { color: #8b4513 !important; }
.text-teal { color: #20c997 !important; }
.text-orange { color: #fd7e14 !important; }
.text-indigo { color: #6610f2 !important; }
.text-red { color: #dc3545 !important; }

.card {
    border-radius: 15px;
    overflow: hidden;
}

.btn-toolbar .btn-group {
    margin-right: 10px;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 20px;
}

.form-select, .form-control {
    border-radius: 8px;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.form-select:focus, .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.tabulator {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.tabulator-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid #dee2e6;
}

.tabulator-row:hover {
    background-color: rgba(0,123,255,0.05) !important;
}

.btn-group .btn {
    border-radius: 0;
}

/* Tab title display logic */
.tab-full-title {
    display: none;
}

.nav-link.active .tab-full-title {
    display: inline;
}

/* Modal tab title display logic */
#editPreviewModal .tab-short-title {
    display: inline;
}

#editPreviewModal .tab-full-title {
    display: none;
}

#editPreviewModal .nav-link.active .tab-short-title {
    display: none;
}

#editPreviewModal .nav-link.active .tab-full-title {
    display: inline;
}

/* Button styling improvements */
.btn {
    margin-right: 5px;
    margin-bottom: 5px;
    border-radius: 5px;
}

/* Responsive design */
@media (max-width: 768px) {
    .tab-full-title {
        display: none !important;
    }

    .btn {
        font-size: 12px;
        padding: 6px 10px;
    }
}
}

.btn-group .btn:first-child {
    border-top-left-radius: 20px;
    border-bottom-left-radius: 20px;
}

.btn-group .btn:last-child {
    border-top-right-radius: 20px;
    border-bottom-right-radius: 20px;
}

.card-header {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-bottom: 1px solid #dee2e6;
}

.form-label {
    color: #495057;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.fw-semibold {
    font-weight: 600;
}

.g-3 > * {
    padding: 0.75rem;
}

.me-1 {
    margin-right: 0.25rem;
}

.me-2 {
    margin-right: 0.5rem;
}

.ms-2 {
    margin-left: 0.5rem;
}

@media (max-width: 768px) {
    .tab-full-title {
        display: none !important;
    }

    .btn-toolbar {
        flex-direction: column;
        gap: 10px;
    }

    .btn-group {
        width: 100%;
    }

    .nav-pills {
        flex-wrap: wrap;
    }

    .nav-pills .nav-item {
        margin-bottom: 5px;
    }

    .card-body .row .col-md-3,
    .card-body .row .col-md-2 {
        margin-bottom: 15px;
    }

    #summaryInfoPanel .row .col-md-8,
    #summaryInfoPanel .row .col-md-4 {
        margin-bottom: 15px;
    }
}

/* Loading animation */
.tabulator-loading {
    background: rgba(255,255,255,0.9);
}

.tabulator-loading .tabulator-loader {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced button styles */
.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.btn:active {
    transform: translateY(0);
}

/* Enhanced alert styles */
.alert {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    border-left: 4px solid #17a2b8;
}

.validation-status-column {
        width: 40px;
        text-align: center;
    }
    .validation-status-valid {
        color: #28a745;
        font-size: 16px;
    }
    .validation-status-invalid {
        color: #dc3545;
        font-size: 16px;
    }
    .validation-error-row {
        background-color: rgba(220, 53, 69, 0.1) !important;
    }
    .validation-tooltip {
        cursor: pointer;
    }

    .validation-error-popup .swal2-popup {
        font-size: 14px;
    }

    .validation-errors table {
        font-size: 13px;
    }

    .validation-errors .table th {
        background-color: #f8f9fa;
        font-weight: 600;
        border-top: 1px solid #dee2e6;
    }

    .validation-errors .table-danger {
        background-color: #f8d7da !important;
    }

    .validation-errors .table-warning {
        background-color: #fff3cd !important;
    }

    .validation-errors code {
        background-color: #e9ecef;
        padding: 2px 4px;
        border-radius: 3px;
        font-size: 12px;
    }

    .validation-errors .alert {
        border-radius: 6px;
        font-size: 14px;
    }

    .validation-errors h6 {
        margin-bottom: 10px;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <!-- Buttons and Filters in one row -->
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="row align-items-end">
                            <!-- Action Buttons -->
                            <div class="col-md-2">
                                <label>Thao tác</label>
                                <div>
                                    <button type="button" class="btn btn-success btn-sm" data-toggle="modal" data-target="#importXmlModal" title="Import XML">
                                        <i class="fas fa-upload"></i>
                                    </button>
                                    <button type="button" id="savePreviewDataBtn" class="btn btn-primary btn-sm" style="display: none;" title="Lưu XML">
                                        <i class="fas fa-save"></i>
                                    </button>
                                    <button type="button" id="exportExcelBtn" class="btn btn-success btn-sm" title="Xuất Excel">
                                        <i class="fas fa-file-excel"></i>
                                    </button>
                                    <button type="button" class="btn btn-danger btn-sm delete-selected-btn" title="Xóa XML">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <button type="button" id="exportMainXmlBtn" class="btn btn-info btn-sm" title="Export XML">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button type="button" id="validate_data_btn" class="btn btn-info btn-sm" title="Kiểm tra hồ sơ XML">
                                        <i class="fas fa-tasks"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Filter Controls -->
                            <div class="col-md-2">
                                <label>Đối tượng KCB</label>
                                <select class="form-control form-control-sm" id="filter_object_type">
                                    <option value="">Tất cả đối tượng</option>
                                    <option value="bhxhvn">BHXH Việt Nam</option>
                                    <option value="bhxhbqp">BHXH Bộ Quốc Phòng</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label>Loại hồ sơ</label>
                                <select class="form-control form-control-sm" id="filter_kcb_type">
                                    <option value="">Tất cả loại hồ sơ</option>
                                    <option value="01">Ngoại trú</option>
                                    <option value="02">Bệnh án ngoại trú</option>
                                    <option value="03">Nội trú</option>
                                </select>
                            </div>
                            <div class="col-md-1">
                                <label>Loại thời gian</label>
                                <select class="form-control form-control-sm" id="filter_time_type">
                                    <option value="ngayTao">Ngày tạo</option>
                                    <option value="ngayTToan">Ngày TT</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label>Từ ngày</label>
                                <input type="date" class="form-control form-control-sm" id="filter_from_date">
                            </div>
                            <div class="col-md-2">
                                <label>Đến ngày</label>
                                <input type="date" class="form-control form-control-sm" id="filter_to_date">
                            </div>
                            <div class="col-md-1">
                                <label>&nbsp;</label>
                                <div>
                                    <button type="button" class="btn btn-primary btn-sm btn-block" id="apply_filters" title="Tìm kiếm">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                    </div>
                </div>

                <!-- Import XML Modal -->
                <div class="modal fade" id="importXmlModal" tabindex="-1" role="dialog" aria-labelledby="importXmlModalLabel" aria-hidden="true">
                    <div class="modal-dialog" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="importXmlModalLabel"><i class="fas fa-upload"></i> Import XML</h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="form-group">
                                    <label for="xml_file"><i class="fas fa-file-code"></i> Chọn file XML</label>
                                    <input type="file" class="form-control-file" id="xml_file" name="xml_file" accept=".xml" multiple>
                                    <small class="form-text text-muted">Có thể chọn nhiều file XML cùng lúc</small>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Hủy</button>
                                <button type="button" class="btn btn-primary" id="uploadXmlBtn">
                                    <i class="fas fa-upload"></i> Tải lên
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- CSRF Token for AJAX requests -->
                {% csrf_token %}

                <!-- XML Tabs Navigation -->
                <div class="card">
                    <div class="card-body p-2">
                        <ul class="nav nav-pills nav-fill" id="xmlTabs" role="tablist">
                            {% if not xml_type or xml_type == 'XML0' %}
                            <li class="nav-item">
                                <a class="nav-link {% if not xml_type or xml_type == 'XML0' %}active{% endif %}"
                                   id="xml0-tab" data-toggle="tab" href="#xml0" role="tab" aria-controls="xml0" aria-selected="true">
                                    <span class="tab-short-title">XML0</span>
                                    <span class="tab-full-title"> - Bảng check-in</span>
                                </a>
                            </li>
                            {% endif %}
                            {% if not xml_type or xml_type == 'XML1' %}
                            <li class="nav-item">
                                <a class="nav-link {% if xml_type == 'XML1' %}active{% endif %}"
                                   id="xml1-tab" data-toggle="tab" href="#xml1" role="tab" aria-controls="xml1" aria-selected="false">
                                    <span class="tab-short-title">XML1</span>
                                    <span class="tab-full-title"> - Thông tin hành chính</span>
                                </a>
                            </li>
                            {% endif %}
                            {% if not xml_type or xml_type == 'XML2' %}
                            <li class="nav-item">
                                <a class="nav-link {% if xml_type == 'XML2' %}active{% endif %}"
                                   id="xml2-tab" data-toggle="tab" href="#xml2" role="tab" aria-controls="xml2" aria-selected="false">
                                    <span class="tab-short-title">XML2</span>
                                    <span class="tab-full-title"> - Thông tin thuốc</span>
                                </a>
                            </li>
                            {% endif %}
                            {% if not xml_type or xml_type == 'XML3' %}
                            <li class="nav-item">
                                <a class="nav-link {% if xml_type == 'XML3' %}active{% endif %}"
                                   id="xml3-tab" data-toggle="tab" href="#xml3" role="tab" aria-controls="xml3" aria-selected="false">
                                    <span class="tab-short-title">XML3</span>
                                    <span class="tab-full-title"> - DVKT, VTYT</span>
                                </a>
                            </li>
                            {% endif %}
                            {% if not xml_type or xml_type == 'XML4' %}
                            <li class="nav-item">
                                <a class="nav-link {% if xml_type == 'XML4' %}active{% endif %} rounded-pill mx-1"
                                   id="xml4-tab" data-toggle="tab" href="#xml4" role="tab" aria-controls="xml4" aria-selected="false">
                                    <span class="tab-short-title">XML4</span>
                                    <span class="tab-full-title"> - Chỉ số CLS</span>
                                </a>
                            </li>
                            {% endif %}
                            {% if not xml_type or xml_type == 'XML5' %}
                            <li class="nav-item">
                                <a class="nav-link {% if xml_type == 'XML5' %}active{% endif %} rounded-pill mx-1"
                                   id="xml5-tab" data-toggle="tab" href="#xml5" role="tab" aria-controls="xml5" aria-selected="false">
                                    <span class="tab-short-title">XML5</span>
                                    <span class="tab-full-title"> - Diễn biến lâm sàng</span>
                                </a>
                            </li>
                            {% endif %}
                            {% if not xml_type or xml_type == 'XML6' %}
                            <li class="nav-item">
                                <a class="nav-link {% if xml_type == 'XML6' %}active{% endif %} rounded-pill mx-1"
                                   id="xml6-tab" data-toggle="tab" href="#xml6" role="tab" aria-controls="xml6" aria-selected="false">
                                    <span class="tab-short-title">XML6</span>
                                    <span class="tab-full-title"> - HIV/AIDS</span>
                                </a>
                            </li>
                            {% endif %}
                            {% if not xml_type or xml_type == 'XML7' %}
                            <li class="nav-item">
                                <a class="nav-link {% if xml_type == 'XML7' %}active{% endif %} rounded-pill mx-1"
                                   id="xml7-tab" data-toggle="tab" href="#xml7" role="tab" aria-controls="xml7" aria-selected="false">
                                    <span class="tab-short-title">XML7</span>
                                    <span class="tab-full-title"> - Giấy ra viện</span>
                                </a>
                            </li>
                            {% endif %}
                            {% if not xml_type or xml_type == 'XML8' %}
                            <li class="nav-item">
                                <a class="nav-link {% if xml_type == 'XML8' %}active{% endif %} rounded-pill mx-1"
                                   id="xml8-tab" data-toggle="tab" href="#xml8" role="tab" aria-controls="xml8" aria-selected="false">
                                    <span class="tab-short-title">XML8</span>
                                    <span class="tab-full-title"> - Tóm tắt HSBA</span>
                                </a>
                            </li>
                            {% endif %}
                            {% if not xml_type or xml_type == 'XML9' %}
                            <li class="nav-item">
                                <a class="nav-link {% if xml_type == 'XML9' %}active{% endif %} rounded-pill mx-1"
                                   id="xml9-tab" data-toggle="tab" href="#xml9" role="tab" aria-controls="xml9" aria-selected="false">
                                    <span class="tab-short-title">XML9</span>
                                    <span class="tab-full-title"> - Giấy chứng sinh</span>
                                </a>
                            </li>
                            {% endif %}
                            {% if not xml_type or xml_type == 'XML10' %}
                            <li class="nav-item">
                                <a class="nav-link {% if xml_type == 'XML10' %}active{% endif %} rounded-pill mx-1"
                                   id="xml10-tab" data-toggle="tab" href="#xml10" role="tab" aria-controls="xml10" aria-selected="false">
                                    <span class="tab-short-title">XML10</span>
                                    <span class="tab-full-title"> - Nghỉ dưỡng thai</span>
                                </a>
                            </li>
                            {% endif %}
                            {% if not xml_type or xml_type == 'XML11' %}
                            <li class="nav-item">
                                <a class="nav-link {% if xml_type == 'XML11' %}active{% endif %} rounded-pill mx-1"
                                   id="xml11-tab" data-toggle="tab" href="#xml11" role="tab" aria-controls="xml11" aria-selected="false">
                                    <span class="tab-short-title">XML11</span>
                                    <span class="tab-full-title"> - Nghỉ hưởng BHXH</span>
                                </a>
                            </li>
                            {% endif %}
                            {% if not xml_type or xml_type == 'XML12' %}
                            <li class="nav-item">
                                <a class="nav-link {% if xml_type == 'XML12' %}active{% endif %} rounded-pill mx-1"
                                   id="xml12-tab" data-toggle="tab" href="#xml12" role="tab" aria-controls="xml12" aria-selected="false">
                                    <span class="tab-short-title">XML12</span>
                                    <span class="tab-full-title"> - Giám định y khoa</span>
                                </a>
                            </li>
                            {% endif %}
                            {% if not xml_type or xml_type == 'XML13' %}
                            <li class="nav-item">
                                <a class="nav-link {% if xml_type == 'XML13' %}active{% endif %} rounded-pill mx-1"
                                   id="xml13-tab" data-toggle="tab" href="#xml13" role="tab" aria-controls="xml13" aria-selected="false">
                                    <span class="tab-short-title">XML13</span>
                                    <span class="tab-full-title"> - Chuyển tuyến BHYT</span>
                                </a>
                            </li>
                            {% endif %}
                            {% if not xml_type or xml_type == 'XML14' %}
                            <li class="nav-item">
                                <a class="nav-link {% if xml_type == 'XML14' %}active{% endif %} rounded-pill mx-1"
                                   id="xml14-tab" data-toggle="tab" href="#xml14" role="tab" aria-controls="xml14" aria-selected="false">
                                    <span class="tab-short-title">XML14</span>
                                    <span class="tab-full-title"> - Hẹn khám lại</span>
                                </a>
                            </li>
                            {% endif %}
                            {% if not xml_type or xml_type == 'XML15' %}
                            <li class="nav-item">
                                <a class="nav-link {% if xml_type == 'XML15' %}active{% endif %} rounded-pill mx-1"
                                   id="xml15-tab" data-toggle="tab" href="#xml15" role="tab" aria-controls="xml15" aria-selected="false">
                                    <span class="tab-short-title">XML15</span>
                                    <span class="tab-full-title"> - Điều trị lao</span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </div>
                    <div class="card-body p-0">
                        <!-- Summary Information Panel -->
                        <div id="summaryInfoPanel" class="alert alert-info m-3" style="display: none;">
                            <div class="row">
                                <div class="col-md-8">
                                    <h6 class="mb-2"><i class="fas fa-info-circle me-2"></i>Thông tin tổng hợp</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <small class="text-muted">Họ tên:</small> <span id="summaryHoTen" class="fw-bold">-</span><br>
                                            <small class="text-muted">Ngày sinh:</small> <span id="summaryNgaySinh" class="fw-bold">-</span><br>
                                            <small class="text-muted">Số CCCD:</small> <span id="summarySoCCCD" class="fw-bold">-</span>
                                        </div>
                                        <div class="col-md-6">
                                            <small class="text-muted">Nhóm máu:</small> <span id="summaryNhomMau" class="fw-bold">-</span><br>
                                            <small class="text-muted">Giới tính:</small> <span id="summaryGioiTinh" class="fw-bold">-</span><br>
                                            <small class="text-muted">Mã quốc tịch:</small> <span id="summaryMaQuocTich" class="fw-bold">-</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <h6 class="mb-2"><i class="fas fa-calculator me-2"></i>Thống kê tài chính</h6>
                                    <div class="row">
                                        <div class="col-6">
                                            <small class="text-muted">Tổng tiền thuốc:</small><br>
                                            <span id="summaryTongTienThuoc" class="fw-bold text-success">0</span><br>
                                            <small class="text-muted">Tổng tiền VTYT:</small><br>
                                            <span id="summaryTongTienVTYT" class="fw-bold text-info">0</span>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">Tổng chi BV:</small><br>
                                            <span id="summaryTongChiBV" class="fw-bold text-primary">0</span><br>
                                            <small class="text-muted">Tổng chi BH:</small><br>
                                            <span id="summaryTongChiBH" class="fw-bold text-warning">0</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="tab-content" id="xmlTabsContent">
                    <!-- XML0 Tab -->
                    {% if not xml_type or xml_type == 'XML0' %}
                    <div class="tab-pane fade {% if not xml_type or xml_type == 'XML0' %}show active{% endif %}" id="xml0" role="tabpanel" aria-labelledby="xml0-tab">
                        <div id="xml0-table" class="xml-tabulator" style="position: relative;"></div>
                    </div>
                    {% endif %}

                    <!-- XML1 Tab -->
                    {% if not xml_type or xml_type == 'XML1' %}
                    <div class="tab-pane fade {% if xml_type == 'XML1' %}show active{% endif %}" id="xml1" role="tabpanel" aria-labelledby="xml1-tab">
                        <div id="xml1-table" class="xml-tabulator" style="position: relative;"></div>
                    </div>
                    {% endif %}

                    <!-- XML2 Tab -->
                    {% if not xml_type or xml_type == 'XML2' %}
                    <div class="tab-pane fade {% if xml_type == 'XML2' %}show active{% endif %}" id="xml2" role="tabpanel" aria-labelledby="xml2-tab">
                        <div id="xml2-table" class="xml-tabulator" style="position: relative;"></div>
                    </div>
                    {% endif %}

                    <!-- XML3 Tab -->
                    {% if not xml_type or xml_type == 'XML3' %}
                    <div class="tab-pane fade {% if xml_type == 'XML3' %}show active{% endif %}" id="xml3" role="tabpanel" aria-labelledby="xml3-tab">
                        <div id="xml3-table" class="xml-tabulator" style="position: relative;"></div>
                    </div>
                    {% endif %}

                    <!-- XML4 Tab -->
                    {% if not xml_type or xml_type == 'XML4' %}
                    <div class="tab-pane fade {% if xml_type == 'XML4' %}show active{% endif %}" id="xml4" role="tabpanel" aria-labelledby="xml4-tab">
                        <div id="xml4-table" class="xml-tabulator" style="position: relative;"></div>
                    </div>
                    {% endif %}

                    <!-- XML5 Tab -->
                    {% if not xml_type or xml_type == 'XML5' %}
                    <div class="tab-pane fade {% if xml_type == 'XML5' %}show active{% endif %}" id="xml5" role="tabpanel" aria-labelledby="xml5-tab">
                        <div id="xml5-table" class="xml-tabulator" style="position: relative;"></div>
                    </div>
                    {% endif %}

                    <!-- XML6 Tab -->
                    {% if not xml_type or xml_type == 'XML6' %}
                    <div class="tab-pane fade {% if xml_type == 'XML6' %}show active{% endif %}" id="xml6" role="tabpanel" aria-labelledby="xml6-tab">
                        <div id="xml6-table" class="xml-tabulator" style="position: relative;"></div>
                    </div>
                    {% endif %}

                    <!-- XML7 Tab -->
                    {% if not xml_type or xml_type == 'XML7' %}
                    <div class="tab-pane fade {% if xml_type == 'XML7' %}show active{% endif %}" id="xml7" role="tabpanel" aria-labelledby="xml7-tab">
                        <div id="xml7-table" class="xml-tabulator" style="position: relative;"></div>
                    </div>
                    {% endif %}

                    <!-- XML8 Tab -->
                    {% if not xml_type or xml_type == 'XML8' %}
                    <div class="tab-pane fade {% if xml_type == 'XML8' %}show active{% endif %}" id="xml8" role="tabpanel" aria-labelledby="xml8-tab">
                        <div id="xml8-table" class="xml-tabulator" style="position: relative;"></div>
                    </div>
                    {% endif %}

                    <!-- XML9 Tab -->
                    {% if not xml_type or xml_type == 'XML9' %}
                    <div class="tab-pane fade {% if xml_type == 'XML9' %}show active{% endif %}" id="xml9" role="tabpanel" aria-labelledby="xml9-tab">
                        <div id="xml9-table" class="xml-tabulator" style="position: relative;"></div>
                    </div>
                    {% endif %}

                    <!-- XML10 Tab -->
                    {% if not xml_type or xml_type == 'XML10' %}
                    <div class="tab-pane fade {% if xml_type == 'XML10' %}show active{% endif %}" id="xml10" role="tabpanel" aria-labelledby="xml10-tab">
                        <div id="xml10-table" class="xml-tabulator" style="position: relative;"></div>
                    </div>
                    {% endif %}

                    <!-- XML11 Tab -->
                    {% if not xml_type or xml_type == 'XML11' %}
                    <div class="tab-pane fade {% if xml_type == 'XML11' %}show active{% endif %}" id="xml11" role="tabpanel" aria-labelledby="xml11-tab">
                        <div id="xml11-table" class="xml-tabulator" style="position: relative;"></div>
                    </div>
                    {% endif %}

                    <!-- XML12 Tab -->
                    {% if not xml_type or xml_type == 'XML12' %}
                    <div class="tab-pane fade {% if xml_type == 'XML12' %}show active{% endif %}" id="xml12" role="tabpanel" aria-labelledby="xml12-tab">
                        <div id="xml12-table" class="xml-tabulator" style="position: relative;"></div>
                    </div>
                    {% endif %}

                    <!-- XML13 Tab -->
                    {% if not xml_type or xml_type == 'XML13' %}
                    <div class="tab-pane fade {% if xml_type == 'XML13' %}show active{% endif %}" id="xml13" role="tabpanel" aria-labelledby="xml13-tab">
                        <div id="xml13-table" class="xml-tabulator" style="position: relative;"></div>
                    </div>
                    {% endif %}

                    <!-- XML14 Tab -->
                    {% if not xml_type or xml_type == 'XML14' %}
                    <div class="tab-pane fade {% if xml_type == 'XML14' %}show active{% endif %}" id="xml14" role="tabpanel" aria-labelledby="xml14-tab">
                        <div id="xml14-table" class="xml-tabulator" style="position: relative;"></div>
                    </div>
                    {% endif %}

                    <!-- XML15 Tab -->
                    {% if not xml_type or xml_type == 'XML15' %}
                    <div class="tab-pane fade {% if xml_type == 'XML15' %}show active{% endif %}" id="xml15" role="tabpanel" aria-labelledby="xml15-tab">
                        <div id="xml15-table" class="xml-tabulator" style="position: relative;"></div>
                    </div>
                    {% endif %}
                </div>
            </div>
            <!-- /.card-body -->
        </div>
        <!-- /.card -->
    </div>
    <!-- /.col -->
</div>
<!-- /.row -->

{% include "xml4750/edit_xml_4750_modal.html" %}
{% endblock %}

{% block extra_js %}
<script src="{% static 'AdminLTE-3.0.1/plugins/bs-custom-file-input/bs-custom-file-input.min.js' %}"></script>
<script src="{% static "tabulator/dist/js/tabulator.min.js" %}"></script>
<script src="https://cdn.jsdelivr.net/npm/luxon@3.4.4/build/global/luxon.min.js"></script>
<script src="{% static "AdminLTE-3.0.1/plugins/select2/js/select2.full.min.js" %}"></script>
<script src="{% static "js/decimal.min.js" %}"></script>
<!-- Thư viện XLSX để xuất Excel -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script>
    // Định nghĩa URL API để sử dụng trong các file JS
    var API_CATEGORY_DATA_URL = "{% url 'danhmuc130:api_category_data' %}";
    var API_OTHER_CATEGORY_DATA_URL = "{% url 'danhmuckhac:api_get_other_category_data' %}";
</script>
<script src="{% static 'js/xml4750/async_utils.js' %}"></script>
<script src="{% static 'js/xml4750/tabulator_xml_func.js' %}"></script>
<script src="{% static 'js/xml4750/tabulator_xml_tables.js' %}"></script>
<script src="{% static 'js/xml4750/xml_reader.js' %}"></script>
<script src="{% static 'js/xml4750/export_xml.js' %}"></script>
<script src="{% static 'js/xml4750/edit_modal.js' %}"></script>
<script>
    // Biến toàn cục để lưu trạng thái bộ lọc hiện tại
    window.currentGlobalFilters = null;
    // Biến toàn cục để lưu trạng thái tab hiện tại
    window.previewedXmlGroup = null;
    window.previewDataMap = null;
    // Biến toàn cục để lưu tình trạng load dữ liệu có phải là upload hay là load từ CSDL
    window.isDataFromUpload = false;
    // Biến toàn cục để kiểm soát việc tải dữ liệu lần đầu
    window.allowInitialTabulatorLoad = false;

    currentActiveTab = 'xml1';

    $(function () {
        // Định nghĩa các định dạng cho từng danh mục
        var categoryFormats = {
            'doituongkcb': 'ma_diengiai',
            'quoctich': 'ma_ten',
            'nghenghiep': 'ma_ten',
            'matainan': 'ma_ten',
            'ketquadieutri': 'ma_ten',
            'loairavien': 'ma_ten'
        };

        // Khởi tạo custom-file-input
        bsCustomFileInput.init();

        // Load dữ liệu danh mục và thiết lập bảng
        loadCategoryData([
            'doituongkcb', 'quoctich', 'nghenghiep', 'matainan', 'ketquadieutri', 'loairavien',
            'tinh', 'quanhuyen', 'xaphuong',
        ], categoryFormats).done(function() {
            // Thêm CSS để ẩn các bảng Tabulator trong tab không hiển thị
            $("<style>")
                .prop("type", "text/css")
                .html(`
                    .tab-pane:not(.active) .tabulator {
                        display: none !important;
                    }
                `)
                .appendTo("head");

            // 2. Xử lý khi chuyển tab - sử dụng refreshTable
            $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                var targetPaneId = $(e.target).attr("href");
                if (targetPaneId) {
                    var tableId = targetPaneId.substring(1) + '-table';
                    
                    // Sử dụng TabulatorXMLTables.refreshTable
                    if (window.TabulatorXMLTables && window.TabulatorXMLTables.refreshTable) {
                        var xmlType = targetPaneId.substring(1).toUpperCase(); // xml1 -> XML1
                        window.TabulatorXMLTables.refreshTable(xmlType);
                    } else {
                        // Fallback
                        var tableElement = document.getElementById(tableId);
                        if (tableElement && tableElement._tabulator) {
                            tableElement._tabulator.redraw(true);
                        }
                    }
                }
            });
            
            // 3. Kích hoạt tab đầu tiên
            var initialActiveTabLink = $('#xmlTabs .nav-link.active');
            if (initialActiveTabLink.length) {
                var targetPaneId = initialActiveTabLink.attr("href");
                if (targetPaneId) {
                    var xmlType = targetPaneId.substring(1).toUpperCase();
                    setTimeout(function() {
                        if (window.TabulatorXMLTables && window.TabulatorXMLTables.refreshTable) {
                            window.TabulatorXMLTables.refreshTable(xmlType);
                        }
                    }, 150);
                }
                
                // Khởi tạo các select trong modal
                initializeConfiguredModalSelects();
                setupCascadingAddressSelects();

                // Khởi tạo Select2
                const select2Options = {
                    theme: 'bootstrap4',
                    width: '100%',
                    dropdownParent: $('#editPreviewModal .modal-body'),
                    dropdownAutoWidth: true,
                };
                $('#modal_xml1_maTinhCuTru').select2(select2Options);
                $('#modal_xml1_maHuyenCuTru').select2(select2Options);
                $('#modal_xml1_maXaCuTru').select2(select2Options);
                $('#modal_xml1_maDanToc').select2(select2Options);
            }
        });

        //3.5 Xử lý nút Save cho dữ liệu từ CSDL
        $(document).on('click', '.save-row-btn', function() {
            var button = $(this);
            var id = button.data('id');
            var rowIndex = button.data('row-index');
            var type = button.data('type');

            // Lấy row data từ tabulator
            var tableElement = document.getElementById(type.toLowerCase() + '-table');
            if (tableElement && tableElement._tabulator) {
                try {
                    var row = null;

                    // Thử lấy row theo array index trước (chính xác hơn)
                    if (rowIndex !== undefined && rowIndex !== null && !isNaN(rowIndex) && rowIndex >= 0) {
                        try {
                            var allRows = tableElement._tabulator.getRows();
                            if (allRows[rowIndex]) {
                                row = allRows[rowIndex];
                            }
                        } catch (error) {
                            console.warn('Could not get row by array index:', error);
                        }
                    }

                    // Nếu không tìm được theo index, thử theo ID
                    if (!row && id) {
                        try {
                            row = tableElement._tabulator.getRow(id);
                        } catch (error) {
                            console.warn('Could not get row by ID:', error);
                        }
                    }

                    if (row) {
                        var rowData = row.getData();

                        // Gửi AJAX request để lưu
                        $.ajax({
                            url: '/xml4750/save_row/',
                            method: 'POST',
                            data: {
                                xml_type: type,
                                row_data: JSON.stringify(rowData)
                            },
                            headers: {
                                'X-CSRFToken': $('[name=csrfmiddlewaretoken]').val()
                            },
                            success: function(response) {
                                if (response.success) {
                                    Swal.fire('Thành công!', response.message, 'success');
                                    // Ẩn nút Save sau khi lưu thành công
                                    button.hide();
                                } else {
                                    Swal.fire('Lỗi!', response.message, 'error');
                                }
                            },
                            error: function(xhr, status, error) {
                                console.error('AJAX Error:', error);
                                Swal.fire('Lỗi!', 'Đã xảy ra lỗi khi lưu dữ liệu: ' + error, 'error');
                            }
                        });
                    } else {
                        console.error('Could not find row to save');
                        Swal.fire('Lỗi!', 'Không thể tìm thấy dòng để lưu.', 'error');
                    }
                } catch (error) {
                    console.error('Error getting row data:', error);
                    Swal.fire('Lỗi!', 'Không thể lấy dữ liệu dòng để lưu.', 'error');
                }
            }
        });

        /* 4.Xử lý xóa XML với các điều kiện:
            TH1: Dữ liệu khi upload từ XML ở dạng xem trước thì Thực hiện việc Xóa trực tiếp trên Tabulator (không cần gọi API)
            TH2: Dữ liệu khi lấy từ CSDL thì thực hiện việc xóa từ CSDL (gọi API) trước nếu xóa thành công thì thực hiện việc xóa trực tiếp trên tabulator và không gọi API
            Nguyên tắc xóa: 
            - Nếu xóa ở XML1 Thì xóa dữ liệu từ XML1-XML15
            - Nếu xóa ở XML0 thì xóa dữ liệu ở XML0
            - Nếu xóa ở XML2-XML15 thì xóa dữ liệu ở bảng đó
        */
        $(document).on('click', '.delete-xml', function() {
            var button = $(this);
            var id = button.data('id');
            var type = button.data('type');
            var rowIndex = button.data('row-index'); // Sử dụng row-index để xác định dòng
            
            console.log('Delete button clicked:', { id, type, rowIndex });
            console.log('Button data attributes:', {
                'data-id': button.data('id'),
                'data-type': button.data('type'),
                'data-row-index': button.data('row-index'),
                'data-ma-lk': button.data('ma-lk'),
                'data-ngay-tao': button.data('ngay-tao')
            });

            // Lấy maLK và ngayTao từ data attributes trước
            var maLK = button.data('maLK') || button.data('ma-lk');
            var ngayTao = button.data('ngayTao') || button.data('ngay-tao');

            console.log('Initial data from button:', { maLK, ngayTao });
            console.log('Final delete data:', { id, type, maLK, ngayTao, rowIndex });
            
            // Nếu không có trong data attributes, thử lấy từ Tabulator row
            if (!maLK || !ngayTao || maLK === 'undefined' || ngayTao === 'undefined') {
                console.log('Data attributes not found, trying to get from Tabulator row...');
                
                // Tìm row element chứa button này
                var rowElement = button.closest('.tabulator-row');
                if (rowElement.length && rowElement[0]._tabulator_row) {
                    var rowData = rowElement[0]._tabulator_row.getData();
                    console.log('Row data from Tabulator:', rowData);
                    
                    maLK = rowData.maLK || rowData.ma_lk;
                    ngayTao = rowData.ngayTao || rowData.ngay_tao;
                    id = id || rowData.id;
                    
                    // Lấy row index từ Tabulator row nếu chưa có
                    if (!rowIndex) {
                        rowIndex = rowElement[0]._tabulator_row.getPosition();
                    }
                }
                
                // Nếu vẫn không có, thử lấy từ active tab và tìm row theo ID hoặc index
                if ((!maLK || !ngayTao) && (id || rowIndex)) {
                    var activeTabPaneId = $('.tab-pane.fade.show.active').attr('id');
                    if (activeTabPaneId) {
                        var tableElement = document.getElementById(activeTabPaneId + '-table');
                        if (tableElement && tableElement._tabulator) {
                            try {
                                var row = null;
                                
                                // Thử tìm theo row index trước (ưu tiên)
                                if (rowIndex !== undefined && rowIndex !== null) {
                                    var allRows = tableElement._tabulator.getRows();
                                    if (allRows[rowIndex]) {
                                        row = allRows[rowIndex];
                                    }
                                }
                                
                                // Nếu không tìm được theo index, thử theo ID
                                if (!row && id) {
                                    row = tableElement._tabulator.getRow(id);
                                }
                                
                                if (row) {
                                    var rowData = row.getData();
                                    console.log('Row data from table lookup:', rowData);
                                    maLK = rowData.maLK || rowData.ma_lk;
                                    ngayTao = rowData.ngayTao || rowData.ngay_tao;
                                    id = id || rowData.id;
                                    rowIndex = rowIndex || row.getPosition();
                                }
                            } catch (error) {
                                console.warn('Could not get row by ID/index:', error);
                            }
                        }
                    }
                }
            }
            
            // Xác định type nếu chưa có
            if (!type) {
                type = $('.tab-pane.fade.show.active').attr('id')?.toUpperCase();
            }

            console.log('Final delete data:', { id, type, maLK, ngayTao, rowIndex });

            // Validate required data
            if (!id && rowIndex === undefined) {
                Swal.fire('Lỗi', 'Không tìm thấy thông tin bản ghi để xóa.', 'error');
                return;
            }

            // Determine delete message based on XML type
            let deleteMessage = '';
            if (type === 'XML0') {
                deleteMessage = `Bạn có chắc chắn muốn xóa bản ghi XML0 này?`;
                if (maLK) deleteMessage += `\nMã LK: ${maLK}`;
                if (ngayTao) deleteMessage += `\nNgày tạo: ${ngayTao}`;
            } else if (type === 'XML1') {
                deleteMessage = `Bạn có chắc chắn muốn xóa toàn bộ hồ sơ này?`;
                if (maLK) deleteMessage += `\nMã LK: ${maLK}`;
                if (ngayTao) deleteMessage += `\nNgày tạo: ${ngayTao}`;
                deleteMessage += `\n\n⚠️ Thao tác này sẽ xóa TẤT CẢ dữ liệu từ XML1-XML15 có cùng Mã LK và Ngày tạo!`;
            } else {
                deleteMessage = `Bạn có chắc chắn muốn xóa bản ghi ${type} này?`;
                if (maLK) deleteMessage += `\nMã LK: ${maLK}`;
                if (ngayTao) deleteMessage += `\nNgày tạo: ${ngayTao}`;
            }

            Swal.fire({
                title: 'Xác nhận xóa',
                text: deleteMessage,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Xóa',
                cancelButtonText: 'Hủy'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Kiểm tra xem có phải dữ liệu preview không - sử dụng nhiều cách để đảm bảo chính xác
                    var isPreviewData = button.hasClass('delete-xml-preview') ||
                                       window.isDataFromXML ||
                                       window.isDataFromUpload ||
                                       (window.currentDataSource && window.currentDataSource.includes('xml'));

                    console.log('Delete action - Data source check:', {
                        hasPreviewClass: button.hasClass('delete-xml-preview'),
                        isDataFromXML: window.isDataFromXML,
                        isDataFromUpload: window.isDataFromUpload,
                        currentDataSource: window.currentDataSource,
                        isPreviewData: isPreviewData,
                        willUseRowIndex: isPreviewData,
                        rowIndex: rowIndex,
                        maLK: maLK,
                        ngayTao: ngayTao
                    });

                    if (isPreviewData) {
                        // Dữ liệu từ upload/preview - xóa trực tiếp từ Tabulator bằng rowIndex
                        console.log(`Preview mode: Deleting ${type} row at index ${rowIndex}`);
                        deleteRowDirectlyFromTabulatorByIndex(type, rowIndex);
                    } else {
                        // Dữ liệu từ CSDL - gửi request lên server với maLK/ngayTao
                        console.log(`Database mode: Deleting ${type} with ID ${id}, maLK ${maLK}, ngayTao ${ngayTao}`);
                        deleteRowFromDatabase(type, id, maLK, ngayTao, rowIndex);
                    }
                }
            });
        });

        // 5. Form validation for import XML - Enhanced with data source management
        $('#import-xml-form').submit(function(e) {
            e.preventDefault();
            var xmlFilesSelected = $('#xml_file')[0].files.length;

            if (xmlFilesSelected === 0) {
                Swal.fire({
                    icon: 'error',
                    title: 'Lỗi',
                    text: 'Vui lòng chọn ít nhất một file XML!',
                });
                return;
            }

            var submitButton = $(this).find('button[type="submit"]');
            submitButton.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Đang nhập...');

            // Update data source state to imported XML
            if (typeof updateDataSourceState === 'function') {
                updateDataSourceState('imported_xml');
            }

            // Client-side parsing using xml_reader.js
            const fileInput = document.getElementById('xml_file');
            const files = fileInput.files;
            const selectedGroup = "XML0-15";

            if (files.length > 0) {
                const parsingPromises = [];
                for (const file of files) {
                    parsingPromises.push(parseXmlFileClientSide(file, selectedGroup));
                }

                Promise.all(parsingPromises)
                    .then(async resultsArray => {
                        $('#importXmlModal').modal('hide');

                        let aggregatedDataMap = {};
                        let masterErrorList = [];
                        let filesProcessedCount = 0;
                        let filesSuccessfullyProcessedWithData = 0;
                        
                        // === SỬ DỤNG TABULATORXMLTABLES ===
                        window.isDataFromUpload = true;
                        window.isDataFromXML = true;
                        
                        // Switch pagination mode sử dụng TabulatorXMLTables
                        if (window.TabulatorXMLTables && window.TabulatorXMLTables.switchPaginationMode) {
                            await window.TabulatorXMLTables.switchPaginationMode(true);
                        }

                        resultsArray.forEach(resultFromFile => {
                            filesProcessedCount++;
                            if (resultFromFile.allErrors && resultFromFile.allErrors.length > 0) {
                                masterErrorList.push(...resultFromFile.allErrors);
                            }

                            const parsedDataMapFromFile = resultFromFile.dataMap;
                            if (parsedDataMapFromFile && Object.keys(parsedDataMapFromFile).length > 0) {
                                filesSuccessfullyProcessedWithData++;
                                for (const xmlTypeKeyInMap in parsedDataMapFromFile) {
                                    if (parsedDataMapFromFile.hasOwnProperty(xmlTypeKeyInMap)) {
                                        if (!aggregatedDataMap[xmlTypeKeyInMap]) {
                                            aggregatedDataMap[xmlTypeKeyInMap] = [];
                                        }
                                        if (Array.isArray(parsedDataMapFromFile[xmlTypeKeyInMap])) {
                                            aggregatedDataMap[xmlTypeKeyInMap] = aggregatedDataMap[xmlTypeKeyInMap].concat(parsedDataMapFromFile[xmlTypeKeyInMap]);
                                        }
                                    }
                                }
                            }
                        });

                        // Display errors if any
                        if (masterErrorList.length > 0) {
                            let errorHtml = '<strong>Đã xảy ra lỗi hoặc cảnh báo khi xử lý tệp XML:</strong><ul style="text-align: left; max-height: 200px; overflow-y: auto;">';
                            masterErrorList.forEach(err => {
                                errorHtml += `<li>${err}</li>`;
                            });
                            errorHtml += '</ul>';
                            Swal.fire({
                                icon: 'warning',
                                title: 'Lỗi hoặc Cảnh báo Xử lý XML',
                                html: errorHtml,
                            });
                        }

                        if (Object.keys(aggregatedDataMap).length > 0) {
                            Swal.fire({
                                icon: 'info',
                                title: `Dữ liệu nhóm ${selectedGroup} đã được đọc từ ${filesSuccessfullyProcessedWithData}/${filesProcessedCount} file(s).`,
                                toast: true, position: 'top-end', showConfirmButton: false, timer: 4500
                            });

                            window.previewedXmlGroup = selectedGroup;
                            window.previewDataMap = aggregatedDataMap;

                            let firstPopulatedTableId = null;

                            // === SỬ DỤNG TABULATORXMLTABLES ===
                            // Load data vào các bảng sử dụng TabulatorXMLTables
                            for (const xmlTypeKeyInMap in aggregatedDataMap) {
                                if (aggregatedDataMap.hasOwnProperty(xmlTypeKeyInMap)) {
                                    const dataForThisType = aggregatedDataMap[xmlTypeKeyInMap];
                                    if (dataForThisType && dataForThisType.length > 0) {
                                        // Sử dụng TabulatorXMLTables để load data
                                        if (window.TabulatorXMLTables && window.TabulatorXMLTables.loadDataIntoTable) {
                                            await window.TabulatorXMLTables.loadDataIntoTable(xmlTypeKeyInMap, dataForThisType);
                                        } else {
                                            // Fallback
                                            let tableId = xmlTypeKeyInMap.toLowerCase() + '-table';
                                            let tableElement = document.getElementById(tableId);
                                            if (tableElement && tableElement._tabulator) {
                                                tableElement._tabulator.setPage(1).then(() => {
                                                    tableElement._tabulator.setData(dataForThisType);
                                                }).catch(err => console.error("Error setting page or data for " + tableId, err));
                                            }
                                        }
                                        
                                        if (!firstPopulatedTableId) {
                                            firstPopulatedTableId = xmlTypeKeyInMap.toLowerCase();
                                        }
                                        console.log(`Client: Populated ${xmlTypeKeyInMap} with ${dataForThisType.length} records from multiple files.`);
                                    }
                                }
                            }
                            
                            if (firstPopulatedTableId) {
                                const tabLink = $('#xmlTabs a[href="#' + firstPopulatedTableId + '"]');
                                if (tabLink.length) {
                                    tabLink.tab('show');
                                }
                            } else {
                                if (masterErrorList.length === 0) {
                                    Swal.fire('Thông báo', 'Không có dữ liệu hợp lệ nào được tìm thấy trong các file cho nhóm đã chọn để hiển thị.', 'info');
                                }
                            }
                            // Hiển thị nút Lưu XML sau khi upload thành công (dù có dữ liệu hay không để user biết là đang ở mode upload)
                            $('#savePreviewDataBtn').show().prop('disabled', Object.keys(aggregatedDataMap).length === 0);

                            // Cập nhật summary footer sau khi upload XML thành công
                            setTimeout(() => {
                                console.log("Updating summaries after XML upload...");
                                if (typeof updateXML1Summary === 'function') {
                                    updateXML1Summary();
                                }
                                if (typeof updateXML2Summary === 'function') {
                                    updateXML2Summary();
                                }
                                if (typeof updateXML3Summary === 'function') {
                                    updateXML3Summary();
                                }

                                // Cập nhật summary cho tab hiện tại
                                const activeTab = document.querySelector('.nav-link.active');
                                if (activeTab) {
                                    const tabId = activeTab.getAttribute('href') || activeTab.getAttribute('data-target');
                                    if (tabId) {
                                        if (tabId.includes('xml2')) {
                                            updateXML2Summary();
                                        } else if (tabId.includes('xml3')) {
                                            updateXML3Summary();
                                        } else {
                                            updateXML1Summary();
                                        }
                                    }
                                }
                            }, 1000);
                        } else {
                            if (masterErrorList.length === 0) {
                                Swal.fire('Thông báo', 'Không có dữ liệu hợp lệ nào được tìm thấy trong các file cho nhóm đã chọn hoặc file không đúng định dạng.', 'info');
                            }
                        }

                        // Hiển thị button xuất Excel sau khi có dữ liệu
                        $('#exportExcelBtn').show();

                        // Cập nhật footer sau khi upload XML
                        setTimeout(function() {
                            updateSummaryInfo();
                        }, 1000);
                    })
                    .catch(error => {
                        $('#importXmlModal').modal('hide');
                        console.error("Client-side parsing failed catastrophically:", error);
                        Swal.fire('Lỗi Nghiêm Trọng!', "Lỗi nghiêm trọng khi xử lý một hoặc nhiều file XML ở trình duyệt: " + error.message, 'error');
                        $('#savePreviewDataBtn').show().prop('disabled', true); // Hiển thị nhưng disable nếu lỗi
                    })
                    .finally(() => {
                        submitButton.prop('disabled', false).html('Nhập dữ liệu');
                        $('#import-xml-form')[0].reset();
                        $('#xml_file').next('.custom-file-label').html('Chọn file (có thể chọn nhiều file)');
                        bsCustomFileInput.init();
                    });
            }

            return false;
        });

        // 6. Save preview data - Enhanced with data source validation
        $('#savePreviewDataBtn').click(function() {
            // Kiểm tra data source
            console.log('Save button clicked. Data source check:', {
                currentDataSource: window.currentDataSource,
                isDataFromUpload: window.isDataFromUpload,
                previewedXmlGroup: window.previewedXmlGroup,
                previewDataMap: window.previewDataMap
            });

            // Use the new data source validation function
            if (typeof handleSaveData === 'function' && !handleSaveData()) {
                return;
            }

            // Thay vì dựa vào previewDataMap, lấy dữ liệu trực tiếp từ Tabulator
            let dataToSaveMap = {};
            let totalRecordsToSave = 0;
            let hasDataToSave = false;

            // Lấy dữ liệu từ tất cả các bảng Tabulator
            for (let i = 0; i <= 15; i++) {
                let xmlTypeKey = 'XML' + i;
                let tableElement = document.getElementById(xmlTypeKey.toLowerCase() + '-table');

                if (tableElement && tableElement._tabulator) {
                    let tableData = tableElement._tabulator.getData();
                    if (tableData && tableData.length > 0) {
                        // Clean data - loại bỏ các field internal của Tabulator
                        let cleanedData = tableData.map(row => {
                            let cleanedRow = {};
                            for (let key in row) {
                                if (row.hasOwnProperty(key) &&
                                    !key.startsWith('_') &&
                                    key !== 'tabulator-row' &&
                                    row[key] !== undefined) {
                                    cleanedRow[key] = row[key] === '' ? null : row[key];
                                }
                            }
                            // Loại bỏ trường 'stt' nếu là XML1
                            if (xmlTypeKey === 'XML1' && cleanedRow.hasOwnProperty('stt')) {
                                delete cleanedRow.stt;
                            }
                            return cleanedRow;
                        });

                        dataToSaveMap[xmlTypeKey] = cleanedData;
                        totalRecordsToSave += cleanedData.length;
                        hasDataToSave = true;
                        console.log(`Found ${cleanedData.length} records in ${xmlTypeKey}`);
                    }
                }
            }

            if (!hasDataToSave) {
                Swal.fire('Lỗi', 'Không có dữ liệu nào trong các bảng để lưu. Vui lòng import XML trước.', 'warning');
                return;
            }

            console.log('Data to save:', dataToSaveMap);

            // Xác định xml_group_processed (mặc định là 'all' nếu không có)
            let xml_group_processed = 'all';
            // Validate data
            let validationErrors = [];
            if (window.TabulatorXMLTables && window.TabulatorXMLTables.validateTableData) {
                for (const [xmlType, records] of Object.entries(dataToSaveMap)) {
                    const validationResult = window.TabulatorXMLTables.validateTableData(xmlType, records);
                    if (validationResult && validationResult.errors && validationResult.errors.length > 0) {
                        validationErrors.push(...validationResult.errors);
                    }
                }
            }

            if (validationErrors.length > 0) {
                console.error('Validation errors:', validationErrors);
                Swal.fire('Lỗi xác thực', 'Dữ liệu không hợp lệ:\n' + validationErrors.slice(0, 5).join('\n') +
                        (validationErrors.length > 5 ? '\n...' : ''), 'error');
                return;
            }

            // Gửi dữ liệu lên server
            Swal.fire({
                title: 'Đang lưu dữ liệu...',
                text: `Đang lưu ${totalRecordsToSave} bản ghi vào cơ sở dữ liệu.`,
                allowOutsideClick: false,
                didOpen: () => { Swal.showLoading(); }
            });

            $.ajax({
                url: "{% url 'xml4750:save_preview_data' %}",
                type: 'POST',
                data: {
                    'csrfmiddlewaretoken': '{{ csrf_token }}',
                    'xml_group_processed': xml_group_processed,
                    'data_to_save_map': JSON.stringify(dataToSaveMap)
                },
                success: function(response) {
                    Swal.close();
                    if (response.success) {
                        Swal.fire('Thành công!', `Đã lưu ${totalRecordsToSave} bản ghi vào cơ sở dữ liệu.`, 'success');

                        // Chuyển sang database mode
                        if (typeof updateDataSourceState === 'function') {
                            updateDataSourceState('database');
                        } else {
                            window.isDataFromUpload = false;
                            window.isDataFromXML = false;
                            $('#savePreviewDataBtn').hide();
                        }
                    } else {
                        Swal.fire('Lỗi!', response.message || 'Không thể lưu dữ liệu.', 'error');
                    }
                },
                error: function(xhr, status, error) {
                    Swal.close();
                    console.error('Save error:', error);
                    Swal.fire('Lỗi!', 'Đã xảy ra lỗi khi lưu dữ liệu: ' + error, 'error');
                }
            });
        });

        $('#savePreviewDataBtn').show(); // Hiển thị nút lưu khi có dữ liệu preview

        // 7. Xử lý bộ lọc dữ liệu - sử dụng loadAllXMLData
        $('#apply_filters').click(async function() {
            // Lưu các bộ lọc hiện tại vào biến toàn cục
            window.currentGlobalFilters = {
                filter_time_type: $('#filter_time_type').val(),
                filter_from_date: $('#filter_from_date').val(),
                filter_to_date: $('#filter_to_date').val(),
                filter_object_type: $('#filter_object_type').val(),
                filter_kcb_type: $('#filter_kcb_type').val()
            };
            
            // Update data source state to database
            if (typeof updateDataSourceState === 'function') {
                updateDataSourceState('database');
            }

            window.isDataFromUpload = false;
            // Clear all table data first, then load from database
            if (typeof clearAllTableData === 'function') {
                clearAllTableData();
            }

            // Use loadFromDatabase function instead
            if (typeof loadFromDatabase === 'function') {
                try {
                    loadFromDatabase();
                } catch (error) {
                    console.error("APPLY_FILTERS: Error in loadFromDatabase:", error);
                    console.error("APPLY_FILTERS: Error stack:", error.stack);

                    // Show user-friendly error message
                    Swal.fire({
                        icon: 'error',
                        title: 'Lỗi tải dữ liệu',
                        text: 'Không thể tải dữ liệu từ cơ sở dữ liệu. Vui lòng thử lại.',
                        timer: 3000
                    });
                }
            } else if (typeof window.loadFromDatabase === 'function') {
                try {
                    window.loadFromDatabase();
                } catch (error) {
                    console.error("APPLY_FILTERS: Error in window.loadFromDatabase:", error);
                    console.error("APPLY_FILTERS: Error stack:", error.stack);
                    // Show user-friendly error message
                    Swal.fire({
                        icon: 'error',
                        title: 'Lỗi tải dữ liệu',
                        text: 'Không thể tải dữ liệu từ cơ sở dữ liệu. Vui lòng thử lại.',
                        timer: 3000
                    });
                }
            } else {
                console.error("APPLY_FILTERS: loadFromDatabase function không khả dụng!");
                console.error("APPLY_FILTERS: Available functions:", Object.getOwnPropertyNames(window).filter(name => name.includes('load')));

                // Show error to user
                Swal.fire({
                    icon: 'error',
                    title: 'Lỗi hệ thống',
                    text: 'Chức năng tải dữ liệu chưa sẵn sàng. Vui lòng tải lại trang.',
                    timer: 3000
                });
            }
        });

        // 8. Xử lý xóa tất cả - sử dụng clearAllTableData
        $(document).on('click', '.delete-all-btn', function() {
            var activeTabLink = $('#xmlTabs .nav-link.active');
            var xmlType = activeTabLink.length ? activeTabLink.attr('href').substring(1).toUpperCase() : null;
            if (!xmlType) {
                Swal.fire('Lỗi', 'Không thể xác định loại XML của tab hiện tại.', 'error');
                return;
            }

            Swal.fire({
                title: 'Xác nhận xóa tất cả',
                text: "Bạn có chắc chắn muốn xóa tất cả dữ liệu " + xmlType + "?",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Xóa tất cả',
                cancelButtonText: 'Hủy'
            }).then((result) => {
                if (result.isConfirmed) {
                    function getCookie(name) {
                        let cookieValue = null;
                        if (document.cookie && document.cookie !== '') {
                            const cookies = document.cookie.split(';');
                            for (let i = 0; i < cookies.length; i++) {
                                const cookie = cookies[i].trim();
                                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                                    break;
                                }
                            }
                        }
                        return cookieValue;
                    }

                    const csrftoken = getCookie('csrftoken');

                    Swal.fire({
                        title: 'Đang xử lý',
                        html: 'Vui lòng đợi trong khi hệ thống xóa dữ liệu...',
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    $.ajax({
                        url: "/xml4750/delete_all/" + xmlType + "/",
                        type: "POST",
                        data: {},
                        headers: {
                            'X-CSRFToken': csrftoken
                        },
                        success: function(response) {
                            if (response.success) {
                                Swal.fire(
                                    'Đã xóa!',
                                    'Tất cả dữ liệu ' + xmlType + ' đã được xóa thành công.',
                                    'success'
                                ).then(() => {
                                    // === SỬ DỤNG TABULATORXMLTABLES ===
                                    // Clear table data và reload
                                    if (window.TabulatorXMLTables && window.TabulatorXMLTables.clearAllTableData) {
                                        window.TabulatorXMLTables.clearAllTableData(xmlType);
                                    }
                                    location.reload();
                                });
                            } else {
                                Swal.fire('Lỗi!', response.message || 'Đã xảy ra lỗi khi xóa dữ liệu.', 'error');
                            }
                        },
                        error: function(xhr, status, error) {
                            Swal.fire('Lỗi!', 'Đã xảy ra lỗi khi xóa dữ liệu: ' + error, 'error');
                        }
                    });
                }
            });
        });

        // Xử lý edit inline
        $(document).on('click', '.edit-inline-btn', function() {
            var button = $(this);
            var rowElement = button.closest('.tabulator-row');
            if (rowElement.length) {
                var tabulatorRow = rowElement[0]._tabulator_row;
                // Enable edit mode cho row
                // Show save và cancel buttons, hide edit button
                button.hide();
                button.siblings('.save-row-btn, .cancel-edit-btn').show();
            }
        });

        // Xử lý khi nhấn nút "Lưu thay đổi" trong modal
        $('#savePreviewChangesBtn').on('click', function() {
            console.log('savePreviewChangesBtn clicked - function started successfully');
            const originalMaLK = $('#editPreviewModal').data('tabulatorRowMaLK');
            const currentMaLK = $('#modal_xml1_maLK').val() || $('#modalEditMaLK').text().trim();

            if (!originalMaLK) {
                Swal.fire('Lỗi', 'Không thể xác định Mã Liên Kết gốc cần cập nhật.', 'error');
                return;
            }

            if (!currentMaLK) {
                Swal.fire('Lỗi', 'Mã Liên Kết hiện tại không được để trống.', 'error');
                return;
            }

            const maLKChanged = (originalMaLK !== currentMaLK);
            console.log(`Save changes: Original maLK=${originalMaLK}, Current maLK=${currentMaLK}, Changed=${maLKChanged}`);

            // *** THÊM: Chuyển đổi dữ liệu BHYT trước khi thu thập ***
            convertBHYTDataBeforeSave();

            let dataFromModal = {};
            // Kiểm tra tất cả XML types có Tabulator trong modal
            const tableXmlTypes = [];
            for (let i = 2; i <= 15; i++) {
                let xmlType = 'XML' + i;
                let modalTableId = 'modal_xml' + i + '-edit-table';
                if (document.getElementById(modalTableId)) {
                    tableXmlTypes.push(xmlType);
                }
            }
            console.log('Found Tabulator XML types in modal:', tableXmlTypes);
            console.log('Current maLK for data collection:', currentMaLK);

            // Thu thập dữ liệu từ tất cả các tab trong modal
            for (let i = 0; i <= 15; i++) {
                let xmlTypeKey = 'XML' + i;

                // Check if tab exists
                const tabExists = $('#modal-edit-' + xmlTypeKey.toLowerCase() + '-tab').length > 0;

                if (tabExists) {
                    if (tableXmlTypes.includes(xmlTypeKey)) {
                        // Lấy dữ liệu từ Tabulator trong modal
                        const modalTableId = 'modal_' + xmlTypeKey.toLowerCase() + '-edit-table';
                        const modalTableElement = document.getElementById(modalTableId);
                        if (modalTableElement && modalTableElement._tabulator) {
                            const tableData = modalTableElement._tabulator.getData();
                            if (tableData && tableData.length > 0) {
                                const updatedTableData = tableData.map(row => ({
                                    ...row,
                                    maLK: currentMaLK
                                }));
                                dataFromModal[xmlTypeKey] = updatedTableData;
                                console.log(`Collected ${updatedTableData.length} records from ${xmlTypeKey} table`);
                            } else {
                                console.log(`Skipping empty Tabulator data for ${xmlTypeKey}.`);
                            }
                        } else {
                            console.log(`Tabulator table ${modalTableId} not found or not initialized`);
                        }
                    } else {
                        // Lấy dữ liệu từ Form trong modal
                        let formData = { maLK: currentMaLK };
                        let hasOtherFields = false;

                        $('#modal-edit-' + xmlTypeKey.toLowerCase() + ' input, #modal-edit-' + xmlTypeKey.toLowerCase() + ' select, #modal-edit-' + xmlTypeKey.toLowerCase() + ' textarea').each(function() {
                            const idAttr = $(this).attr('id');
                            if (idAttr && idAttr.startsWith('modal_' + xmlTypeKey.toLowerCase() + '_')) {
                                const fieldName = idAttr.replace('modal_' + xmlTypeKey.toLowerCase() + '_', '');
                                
                                // *** SKIP _text inputs vì chúng chỉ dùng để hiển thị ***
                                if (fieldName.endsWith('_text')) {
                                    console.log(`Skipping _text input: ${fieldName}`);
                                    return; // continue to next iteration
                                }
                                
                                let value = $(this).val();

                                // *** SPECIAL HANDLING FOR BHYT FIELDS ***
                                if (xmlTypeKey === 'XML1' && (fieldName === 'gtTheTu' || fieldName === 'gtTheDen')) {
                                    console.log(`Special handling for BHYT field: ${fieldName}`);
                                    value = getBHYTFieldValue(fieldName);
                                    console.log(`BHYT field ${fieldName} processed value:`, value);
                                }
                                // *** APPLY FIELD TRANSFORMERS FOR OTHER FIELDS ***
                                else if (fieldTransformers[xmlTypeKey] && fieldTransformers[xmlTypeKey][fieldName] && fieldTransformers[xmlTypeKey][fieldName].fromModal) {
                                    console.log(`Applying transformer for ${xmlTypeKey}.${fieldName}, original value:`, value);
                                    value = fieldTransformers[xmlTypeKey][fieldName].fromModal(value);
                                    console.log(`Transformed value:`, value);
                                    // Ensure transformed value is not undefined, convert to null if needed for empty strings
                                    if (value === undefined || value === '') {
                                        value = null;
                                    }
                                } else {
                                    // *** EXISTING LOGIC FOR EMPTY NUMERIC/SELECT/DATE FIELDS IF NO TRANSFORMER ***
                                    if (($(this).attr('type') === 'number' || $(this).is('select')) && value === '') {
                                        const numericFields = ['gioiTinh', 'maDinhChiThai', 'tuoiThai', 'soNgayNghi', 'soConChet', 'ketQuaDtri', 'maLoaiRV', 'maTaiNan', 'soNgayDtri'];
                                        const selectFieldsToInt = ['gioiTinh'];
                                        if (numericFields.includes(fieldName) || selectFieldsToInt.includes(fieldName)) {
                                            value = null;
                                        }
                                    } else if ($(this).attr('type') === 'datetime-local' && value === '') {
                                        value = null;
                                    } else if ($(this).attr('type') === 'date' && value === '') {
                                        value = null;
                                    }
                                    if ($(this).attr('type') === 'number' && value === '' && ['tThuoc', 'tVTYT', 'tTongChiBV', 'tTongChiBH', 'tBNTT', 'tBNCCT', 'tBHTT', 'tNguonKhac', 'tBHTTGDV'].includes(fieldName)) {
                                        value = null;
                                    }
                                }

                                formData[fieldName] = value;
                                // Check if the value is not null/undefined/empty string (after cleaning)
                                if (value !== null && value !== undefined && value !== '') {
                                    hasOtherFields = true;
                                }
                                
                                console.log(`Field ${fieldName}: ${value} (hasOtherFields: ${hasOtherFields})`);
                            }
                        });
                        
                        // For form-based XML, include data only if maLK is present AND (it's XML1 OR there are other fields with values)
                        if (currentMaLK && (xmlTypeKey === 'XML1' || hasOtherFields)) {
                            dataFromModal[xmlTypeKey] = [formData]; // Wrap in array for consistency
                            console.log(`Collected form data for ${xmlTypeKey}:`, formData);
                        } else {
                            console.log(`Skipping empty form data for ${xmlTypeKey} (only maLK present or no other fields have values).`);
                        }
                    }
                } else {
                    console.log(`Tab ${xmlTypeKey} does not exist in modal`);
                }
            }

            // Rest of the function remains the same...
            console.log('Final dataFromModal:', dataFromModal);
            
            // Check data source properly
            const isFromXMLPreview = window.isDataFromXML || window.isDataFromUpload ||
                                (window.currentDataSource && window.currentDataSource.includes('xml'));

            console.log('Data source check:', {
                isDataFromXML: window.isDataFromXML,
                isDataFromUpload: window.isDataFromUpload,
                currentDataSource: window.currentDataSource,
                isFromXMLPreview: isFromXMLPreview
            });

            // Ensure we always have XML1 data for database operations
            if (!isFromXMLPreview && !dataFromModal['XML1'] && currentMaLK) {
                console.log('Adding minimal XML1 data for database operation');
                dataFromModal['XML1'] = [{ maLK: currentMaLK }];
            }

            // Validate that we have some data to save
            if (Object.keys(dataFromModal).length === 0) {
                Swal.fire('Lỗi', 'Không có dữ liệu nào để lưu. Vui lòng kiểm tra lại thông tin.', 'error');
                return;
            }

            // Additional validation for database operations
            if (!isFromXMLPreview) {
                if (!dataFromModal['XML1'] || !Array.isArray(dataFromModal['XML1']) || dataFromModal['XML1'].length === 0) {
                    console.error('Missing XML1 data for database operation');
                    Swal.fire('Lỗi', 'Thiếu Mã Liên Kết hoặc dữ liệu chính sửa. Vui lòng kiểm tra lại.', 'error');
                    return;
                }
            }

            // ... rest of the function remains the same ...
            
            if (isFromXMLPreview) {
                // Dữ liệu từ upload: Cập nhật Tabulator ở client
                let updatedSuccessfully = false;

                if (maLKChanged) {
                    // Nếu maLK thay đổi, cần xóa tất cả dữ liệu cũ và thêm dữ liệu mới
                    console.log(`maLK changed from ${originalMaLK} to ${currentMaLK}, updating all related data`);

                    // Xóa tất cả dữ liệu có originalMaLK
                    for (let i = 0; i <= 15; i++) {
                        const xmlType = `XML${i}`;
                        const tableId = xmlType.toLowerCase() + '-table';
                        const tableElement = document.getElementById(tableId);
                        if (tableElement && tableElement._tabulator) {
                            const oldRows = tableElement._tabulator.getRows().filter(row => row.getData().maLK === originalMaLK);
                            oldRows.forEach(row => row.delete());
                            console.log(`Deleted ${oldRows.length} rows with originalMaLK ${originalMaLK} from ${xmlType}`);
                        }
                    }

                    // Thêm dữ liệu mới với currentMaLK
                    for (const xmlTypeKey in dataFromModal) {
                        if (dataFromModal.hasOwnProperty(xmlTypeKey)) {
                            const tableId = xmlTypeKey.toLowerCase() + '-table';
                            const tableElement = document.getElementById(tableId);
                            if (tableElement && tableElement._tabulator) {
                                const recordsToAdd = dataFromModal[xmlTypeKey];
                                tableElement._tabulator.addData(recordsToAdd);
                                updatedSuccessfully = true;
                                console.log(`Added ${recordsToAdd.length} new records to ${xmlTypeKey} with maLK ${currentMaLK}`);

                                // Cập nhật data-ma-lk của các nút edit trong bảng mới
                                setTimeout(() => {
                                    tableElement.querySelectorAll('.edit-xml-all-btn').forEach(btn => {
                                        const rowData = btn.closest('.tabulator-row')?._tabulator_row?.getData();
                                        if (rowData && rowData.maLK === currentMaLK) {
                                            btn.setAttribute('data-ma-lk', currentMaLK);
                                        }
                                    });
                                }, 100);
                            }
                        }
                    }
                } else {
                    // maLK không thay đổi, cập nhật như cũ
                    for (const xmlTypeKey in dataFromModal) {
                        if (dataFromModal.hasOwnProperty(xmlTypeKey)) {
                            const tableId = xmlTypeKey.toLowerCase() + '-table';
                            const tableElement = document.getElementById(tableId);
                            if (tableElement && tableElement._tabulator) {
                                const tabulatorInstance = tableElement._tabulator;
                                const recordsToUpdate = dataFromModal[xmlTypeKey];

                                if (!tableXmlTypes.includes(xmlTypeKey) && recordsToUpdate.length > 0) {
                                    const rowsInMainTable = tabulatorInstance.getRows().filter(row => row.getData().maLK === originalMaLK);
                                    if (rowsInMainTable.length > 0) {
                                        rowsInMainTable[0].update(recordsToUpdate[0]);
                                        updatedSuccessfully = true;
                                    }
                                } else if (tableXmlTypes.includes(xmlTypeKey)) {
                                    const oldRows = tabulatorInstance.getRows().filter(row => row.getData().maLK === originalMaLK);
                                    oldRows.forEach(row => row.delete());
                                    tabulatorInstance.addData(recordsToUpdate);
                                    updatedSuccessfully = true;
                                }
                                console.log(`Updated data in ${tableId} for maLK ${originalMaLK}`);
                            }
                        }
                    }
                }
                if (updatedSuccessfully) {
                    Swal.fire('Thành công', 'Dữ liệu xem trước đã được cập nhật. Nhấn "Lưu XML" để lưu vào CSDL.', 'success');
                } else {
                    Swal.fire('Thông báo', 'Không có thay đổi nào được thực hiện hoặc không tìm thấy dòng để cập nhật trong bản xem trước.', 'info');
                }
            } else {
                // Dữ liệu từ CSDL: Gửi AJAX lên server
                Swal.fire({
                    title: 'Đang lưu vào CSDL...',
                    text: 'Vui lòng chờ.',
                    allowOutsideClick: false,
                    didOpen: () => { Swal.showLoading(); }
                });
                $.ajax({
                    url: "{% url 'xml4750:save_edited_data_from_modal' %}", // Cần tạo URL và view này
                    type: 'POST',
                    data: {
                        'csrfmiddlewaretoken': '{{ csrf_token }}',
                        'original_maLK': originalMaLK,
                        'current_maLK': currentMaLK,
                        'maLK_changed': maLKChanged,
                        'edited_data_map': JSON.stringify(dataFromModal)
                    },
                    success: function(response) {
                        Swal.close();
                        if (response.success) {
                            Swal.fire('Thành công!', response.message || 'Dữ liệu đã được lưu vào CSDL.', 'success');

                            // Cập nhật dữ liệu client-side thay vì reload
                            console.log('Updating client-side data after save success');

                            if (maLKChanged && originalMaLK !== currentMaLK) {
                                // Nếu maLK thay đổi, xóa dữ liệu cũ và thêm dữ liệu mới
                                console.log(`maLK changed from ${originalMaLK} to ${currentMaLK}, updating all tables`);

                                // Xóa tất cả dữ liệu có originalMaLK
                                for (let i = 0; i <= 15; i++) {
                                    const xmlType = `XML${i}`;
                                    const tableId = xmlType.toLowerCase() + '-table';
                                    const tableElement = document.getElementById(tableId);
                                    if (tableElement && tableElement._tabulator) {
                                        const oldRows = tableElement._tabulator.getRows().filter(row => row.getData().maLK === originalMaLK);
                                        oldRows.forEach(row => row.delete());
                                        console.log(`Deleted ${oldRows.length} rows with originalMaLK ${originalMaLK} from ${xmlType}`);
                                    }
                                }

                                // Thêm dữ liệu mới với currentMaLK
                                for (const xmlTypeKey in dataFromModal) {
                                    if (dataFromModal.hasOwnProperty(xmlTypeKey)) {
                                        const tableId = xmlTypeKey.toLowerCase() + '-table';
                                        const tableElement = document.getElementById(tableId);
                                        if (tableElement && tableElement._tabulator) {
                                            const recordsToAdd = dataFromModal[xmlTypeKey];
                                            if (recordsToAdd && recordsToAdd.length > 0) {
                                                tableElement._tabulator.addData(recordsToAdd);
                                                console.log(`Added ${recordsToAdd.length} new records to ${xmlTypeKey} with maLK ${currentMaLK}`);
                                            }
                                        }
                                    }
                                }
                            } else {
                                // maLK không thay đổi, cập nhật dữ liệu hiện có
                                console.log(`maLK unchanged (${currentMaLK}), updating existing records`);

                                for (const xmlTypeKey in dataFromModal) {
                                    if (dataFromModal.hasOwnProperty(xmlTypeKey)) {
                                        const tableId = xmlTypeKey.toLowerCase() + '-table';
                                        const tableElement = document.getElementById(tableId);
                                        if (tableElement && tableElement._tabulator) {
                                            const tabulatorInstance = tableElement._tabulator;
                                            const newData = dataFromModal[xmlTypeKey];

                                            if (newData && newData.length > 0) {
                                                // Xóa dữ liệu cũ có cùng maLK
                                                const oldRows = tabulatorInstance.getRows().filter(row => row.getData().maLK === currentMaLK);
                                                oldRows.forEach(row => row.delete());

                                                // Thêm dữ liệu mới
                                                tabulatorInstance.addData(newData);
                                                console.log(`Updated ${newData.length} records in ${xmlTypeKey} for maLK ${currentMaLK}`);
                                            }
                                        }
                                    }
                                }
                            }

                            // Cập nhật summary cho tất cả các XML types
                            setTimeout(() => {
                                if (typeof updateXML1Summary === 'function') {
                                    updateXML1Summary();
                                }
                                if (typeof updateXML2Summary === 'function') {
                                    updateXML2Summary();
                                }
                                if (typeof updateXML3Summary === 'function') {
                                    updateXML3Summary();
                                }
                            }, 500);
                        } else {
                            Swal.fire('Lỗi!', response.message || 'Không thể lưu dữ liệu vào CSDL.', 'error');
                        }
                    },
                    error: function(xhr) {
                        Swal.close();
                        let errorMsg = 'Lỗi kết nối hoặc lỗi máy chủ.';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMsg = xhr.responseJSON.message;
                        } else if (xhr.responseText) {
                            try { errorMsg = JSON.parse(xhr.responseText).message || errorMsg; } catch (e) { /* ignore */ }
                        }
                        Swal.fire('Lỗi!', errorMsg, 'error');
                    }
                });
            }
            $('#editPreviewModal').modal('hide');
        });

        //Thực hiện việc tính toán và cập nhật lại dữ liệu trên modal
        $('#recalculatorBtn').on('click', function(){
            try {
                // Lấy dữ liệu từ các bảng modal
                const xml2TableElement = document.getElementById('modal_xml2-edit-table');
                const xml3TableElement = document.getElementById('modal_xml3-edit-table');
                
                if (!xml2TableElement || !xml2TableElement._tabulator) {
                    console.error('XML2 table not found or not initialized');
                    alert('Bảng XML2 chưa được khởi tạo');
                    return;
                }
                
                if (!xml3TableElement || !xml3TableElement._tabulator) {
                    console.error('XML3 table not found or not initialized');
                    alert('Bảng XML3 chưa được khởi tạo');
                    return;
                }
                
                // Lấy dữ liệu từ các bảng
                const xml2Data = xml2TableElement._tabulator.getData();
                const xml3Data = xml3TableElement._tabulator.getData();
                
                console.log('Data retrieved:', {
                    xml2Count: xml2Data.length,
                    xml3Count: xml3Data.length
                });
                
                // --- Step 1: Collect XML1 data from the modal ---
                const maLKFromModal = $('#modalEditMaLK').text();
                let xml1Record = { maLK: maLKFromModal };

                $('#modal-edit-xml1').find('input, select, textarea').each(function() {
                    const el = $(this);
                    const id = el.attr('id');
                    if (id && id.startsWith('modal_xml1_')) {
                        const fieldName = id.substring('modal_xml1_'.length);
                        let value = el.val();
                        const inputType = el.attr('type');

                        // Convert date/datetime values to standard XML formats
                        if (inputType === 'date' && value) { // YYYY-MM-DD to YYYYMMDD
                            value = value.replace(/-/g, '');
                        } else if (inputType === 'datetime-local' && value) { // YYYY-MM-DDTHH:mm to YYYYMMDDHHmm
                            value = value.replace(/-/g, '').replace('T', '').replace(':', '');
                        } else if (inputType === 'number' && value !== '') {
                            value = parseFloat(value);
                        }
                        // Assign value, ensuring null/undefined becomes empty string for consistency if needed
                        xml1Record[fieldName] = (value === undefined || value === null) ? "" : value;
                    }
                });

                // Validate if essential XML1 data was collected
                if (!xml1Record.maBN && !xml1Record.hoTen) { // Example: check if maBN or hoTen is missing
                    console.error('XML1 data from modal seems incomplete for recalculation.', xml1Record);
                    alert('Dữ liệu XML1 (ví dụ: Mã BN, Họ Tên) từ modal không đủ để tính toán. Vui lòng kiểm tra lại.');
                    return;
                }
                
                // Lấy giá trị tuyenBV (có thể từ biến global hoặc từ dữ liệu)
                const tuyenBVValue = typeof tuyenbv !== 'undefined' ? tuyenbv : '1'; // Default là '1' nếu không có
                
                // Gọi hàm tính toán (từ edit_modal.js)
                const result = reCalcualteOneXML(xml1Record, xml2Data, xml3Data, tuyenBVValue);
                
                console.log('Recalculation result:', result);
                
                // Cập nhật kết quả lên giao diện modal
                if (result) {
                    // --- Step 3: Update Modal Display ---
                    // Assuming 'result' contains the updated XML1 fields or the specific totals.
                    // Use field names from XML1 (e.g., tTongChiBV, tThuoc) if result is the updated xml1Record.
                    // Or use keys from 'result' object as per its definition.
                    // The original code used result.thanhTienBV, result.bhtt etc.

                    const formatDisplayNumber = (value) => (value !== null && value !== undefined ? parseFloat(value) : 0).toLocaleString('vi-VN');
                    const getNumericValue = (value) => (value !== null && value !== undefined ? parseFloat(value) : 0);

                    // Sử dụng trực tiếp các key từ result đã được chuẩn hóa
                    const val_tTongChiBV = getNumericValue(result.tTongChiBV);
                    const val_tTongChiBH = getNumericValue(result.tTongChiBH);
                    const val_tBHTT = getNumericValue(result.tBHTT);
                    const val_tBNCCT = getNumericValue(result.tBNCCT);
                    const val_tBNTT = getNumericValue(result.tBNTT);
                    const val_tNguonKhac = getNumericValue(result.tNguonKhac);
                    const val_tBHTTGDV = getNumericValue(result.tBHTTGDV); // Nếu có
                    const val_tThuoc = getNumericValue(result.tThuoc);
                    const val_tVTYT = getNumericValue(result.tVTYT);

                    $('#modalEditTongChiBV').text(formatDisplayNumber(val_tTongChiBV));
                    $('#modal_xml1_tTongChiBV').val(val_tTongChiBV);
                    $('#modalEditTongChiBH').text(formatDisplayNumber(val_tTongChiBH));
                    $('#modal_xml1_tTongChiBH').val(val_tTongChiBH);
                    $('#modalEditTongBHTT').text(formatDisplayNumber(val_tBHTT));
                    $('#modal_xml1_tBHTT').val(val_tBHTT);
                    $('#modalEditTongBNCCT').text(formatDisplayNumber(val_tBNCCT));
                    $('#modal_xml1_tBNCCT').val(val_tBNCCT);
                    $('#modalEditTongBNTT').text(formatDisplayNumber(val_tBNTT));
                    $('#modal_xml1_tBNTT').val(val_tBNTT);
                    $('#modalEditTongTienThuoc').text(formatDisplayNumber(val_tThuoc));
                    $('#modal_xml1_tThuoc').val(val_tThuoc);
                    $('#modalEditTongTienVTYT').text(formatDisplayNumber(val_tVTYT));
                    $('#modal_xml1_tVTYT').val(val_tVTYT);
                    // Cập nhật cho tNguonKhac và tBHTTGDV nếu có input ẩn
                    $('#modal_xml1_tNguonKhac').val(val_tNguonKhac);
                    $('#modal_xml1_tBHTTGDV').val(val_tBHTTGDV);
                    
                    // Cập nhật lại dữ liệu trong các bảng (nếu có thay đổi)
                    xml2TableElement._tabulator.setData(xml2Data);
                    xml3TableElement._tabulator.setData(xml3Data);
                    
                    // Hiển thị thông báo thành công
                    console.log('Recalculation completed successfully');
                    
                    // Có thể thêm notification hoặc highlight để user biết đã tính toán xong
                    $(this).addClass('btn-success').removeClass('btn-primary');
                    setTimeout(() => {
                        $(this).removeClass('btn-success').addClass('btn-primary');
                    }, 2000);
                    
                } else {
                    console.error('Recalculation returned null/undefined result');
                    alert('Có lỗi xảy ra trong quá trình tính toán');
                }
                
            } catch (error) {
                console.error('Error during recalculation:', error);
                alert('Có lỗi xảy ra: ' + error.message);
            }
        });

        // Xử lý tab switching trong modal để không ảnh hưởng đến tab chính
        $('#modalEditXmlTabs a[data-toggle="tab"]').on('click', function (e) {
            e.preventDefault();
            e.stopPropagation();
            
            // Remove active class from all modal tabs
            $('#modalEditXmlTabs .nav-link').removeClass('active');
            $('#modalEditXmlTabsContent .tab-pane').removeClass('show active');
            
            // Add active class to clicked tab
            $(this).addClass('active');
            var targetTab = $(this).attr('href');
            $(targetTab).addClass('show active');
            
            // Redraw Tabulator table ở tab vừa được show (nếu có)
            var tabName = targetTab.replace('#', '');
            var xmlType = tabName.replace('modal-edit-', '');
            var modalTableId = 'modal_' + xmlType + '-edit-table';
            var modalTableElement = document.getElementById(modalTableId);
            if (modalTableElement && modalTableElement._tabulator) {
                modalTableElement._tabulator.redraw(true);
            }
        });
        
        // Xử lý nút "Sửa hồ sơ" (.edit-xml-all-btn) trên các dòng của Tabulator
        $(document).on('click', '.edit-xml-all-btn', function() {
            const maLK = $(this).data('ma-lk');
            const ngayTao = $(this).data('ngay-tao');
            if (maLK && typeof openEditPreviewModal === 'function') {
                openEditPreviewModal(maLK, ngayTao);
            } else {
                console.error('Không thể mở modal: maLK hoặc hàm openEditPreviewModal không tồn tại.');
                Swal.fire('Lỗi', 'Không thể thực hiện thao tác sửa.', 'error');
            }
        });

        // Xử lý xóa dòng trong các bảng Tabulator của MODAL (XML2, XML3, XML4, XML5)
        $(document).on('click', '#editPreviewModal .delete-modal-xml-item', function() {
            var $button = $(this);
            var modalRowId = $button.data('id'); // Đây là index của dòng trong Tabulator của modal
            var xmlType = $button.data('type'); // Ví dụ: XML2, XML3

            // Tìm instance Tabulator của bảng trong modal
            var modalTableId = 'modal_' + xmlType.toLowerCase() + '-edit-table';
            var modalTableElement = document.getElementById(modalTableId);

            if (!modalTableElement || !modalTableElement._tabulator) {
                console.error('Modal Tabulator instance not found for:', modalTableId);
                Swal.fire('Lỗi', 'Không tìm thấy bảng dữ liệu trong modal để xóa.', 'error');
                return;
            }
            var modalTabulatorInstance = modalTableElement._tabulator;

            Swal.fire({
                title: 'Xác nhận xóa dòng',
                text: `Bạn có chắc muốn xóa dòng này khỏi bảng ${xmlType} trong cửa sổ xem trước không?`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Xóa khỏi xem trước',
                cancelButtonText: 'Hủy'
            }).then((result) => {
                if (result.isConfirmed) {
                    var rowToDeleteInModal = modalTabulatorInstance.getRow(modalRowId);
                    if (rowToDeleteInModal) {
                        rowToDeleteInModal.delete().then(() => {
                            Swal.fire({
                                title: 'Đã xóa!',
                                text: `Dòng đã được xóa khỏi bảng ${xmlType} trong xem trước.`,
                                icon: 'success',
                                toast: true,
                                position: 'top-end',
                                showConfirmButton: false,
                                timer: 2000
                            });

                            // Cập nhật lại STT cho các dòng còn lại
                            modalTabulatorInstance.getRows().forEach((row, index) => {
                                row.update({ stt: index + 1 }); // Cập nhật trường STT
                            });
                            // Nếu việc xóa XML2, XML3, XML4, XML5 ảnh hưởng đến tổng XML1, hãy gọi tính toán lại
                            if (['XML2', 'XML3', 'XML4', 'XML5'].includes(xmlType)) {
                                $('#recalculatorBtn').trigger('click');
                            }
                        }).catch(err => {
                            console.error('Lỗi khi xóa dòng khỏi Tabulator trong modal:', err);
                            Swal.fire('Lỗi', `Không thể xóa dòng khỏi bảng ${xmlType} trong modal.`, 'error');
                        });
                    } else {
                        console.error('Không tìm thấy dòng trong Tabulator modal với ID:', modalRowId, 'cho bảng:', modalTableId);
                        Swal.fire('Lỗi', `Không tìm thấy dòng để xóa trong bảng ${xmlType} của modal.`, 'error');
                    }
                }
            });
        });

        // Xử lý thay đổi maLK - đồng bộ với tất cả XML types
        $(document).on('change', '#modal_xml1_maLK', function() {
            const newMaLK = $(this).val();
            const oldMaLK = $('#modalEditMaLK').text().trim();

            console.log(`maLK changed from ${oldMaLK} to ${newMaLK}`);

            if (newMaLK && newMaLK !== oldMaLK) {
                // Cập nhật tiêu đề modal
                $('#modalEditMaLK').text(newMaLK);

                // Đồng bộ maLK trong tất cả các XML types trong modal
                syncMaLKInAllTabs(oldMaLK, newMaLK);

                // Hiển thị thông báo
                showNotification(`Đã cập nhật Mã Liên Kết từ ${oldMaLK} thành ${newMaLK}`, 'info');
            }
        });

        // Hàm đồng bộ maLK trong tất cả các tab
        function syncMaLKInAllTabs(oldMaLK, newMaLK) {
            console.log(`syncMaLKInAllTabs: ${oldMaLK} -> ${newMaLK}`);

            // Đồng bộ trong các Tabulator tables trong modal (XML2, XML3, XML4, XML5, XML6, XML9, XML15)
            const modalTableXmlTypes = ['XML2', 'XML3', 'XML4', 'XML5', 'XML6', 'XML9', 'XML15'];

            modalTableXmlTypes.forEach(xmlType => {
                // Modal tables có ID pattern: modal_xml2-edit-table, modal_xml3-edit-table, etc.
                const modalTableElementId = `modal_${xmlType.toLowerCase()}-edit-table`;
                const modalTableElement = document.getElementById(modalTableElementId);

                if (modalTableElement && modalTableElement._tabulator) {
                    try {
                        const data = modalTableElement._tabulator.getData();
                        const updatedData = data.map(row => {
                            if (row.maLK === oldMaLK) {
                                return { ...row, maLK: newMaLK };
                            }
                            return row;
                        });

                        modalTableElement._tabulator.setData(updatedData);
                        console.log(`Updated maLK in modal ${xmlType} table: ${updatedData.length} rows`);
                    } catch (error) {
                        console.error(`Error updating maLK in modal ${xmlType} table:`, error);
                    }
                } else {
                    console.warn(`Modal table ${modalTableElementId} not found or not initialized`);
                }
            });

            // Đồng bộ trong các input fields khác (XML0, XML1, XML7, XML8, XML10, XML11, XML12, XML13, XML14)
            const inputXmlTypes = ['XML0', 'XML1', 'XML7', 'XML8', 'XML10', 'XML11', 'XML12', 'XML13', 'XML14'];

            inputXmlTypes.forEach(xmlType => {
                const maLKInput = $(`#modal_${xmlType.toLowerCase()}_maLK`);
                if (maLKInput.length > 0) {
                    const oldValue = maLKInput.val();
                    if (oldValue === oldMaLK || oldValue === '') {
                        maLKInput.val(newMaLK);
                        console.log(`Updated maLK in ${xmlType} input field: ${oldValue} -> ${newMaLK}`);
                    }
                } else {
                    console.warn(`Input field #modal_${xmlType.toLowerCase()}_maLK not found`);
                }
            });

            // Đồng bộ trong main tables nếu đang ở preview mode
            if (window.isDataFromXML || window.currentDataSource === 'imported_xml') {
                console.log('Syncing maLK in main tables (preview mode)');

                for (let i = 0; i <= 15; i++) {
                    const xmlType = `XML${i}`;
                    const mainTableElementId = xmlType.toLowerCase() + '-table';
                    const mainTableElement = document.getElementById(mainTableElementId);

                    if (mainTableElement && mainTableElement._tabulator) {
                        try {
                            const data = mainTableElement._tabulator.getData();
                            const updatedData = data.map(row => {
                                if (row.maLK === oldMaLK) {
                                    return { ...row, maLK: newMaLK };
                                }
                                return row;
                            });

                            if (updatedData.some(row => row.maLK === newMaLK)) {
                                mainTableElement._tabulator.setData(updatedData);
                                console.log(`Updated maLK in main ${xmlType} table`);
                            }
                        } catch (error) {
                            console.error(`Error updating maLK in main ${xmlType} table:`, error);
                        }
                    }
                }
            }
        }

        // Thực hiện export dữ liệu trên modal
        $('#exportXMLBtn').on('click', function(e) {
            // Lấy maLK trực tiếp từ trường input của XML1 trong modal
            let maLK = $('#modal_xml1_maLK').val();

            // Fallback: nếu không lấy được từ input XML1, thử lấy từ tiêu đề modal
            if (!maLK || maLK.trim() === "") {
                maLK = $('#modalEditMaLK').text().trim();
            }
            if (!maLK) {
                Swal.fire('Lỗi', 'Không tìm thấy Mã Liên Kết trong modal để xuất XML.', 'error');
                return;
            }

            Swal.fire({
                title: 'Đang chuẩn bị dữ liệu...',
                text: 'Vui lòng đợi trong khi hệ thống thu thập và tạo file XML.',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // 1. Thu thập dữ liệu từ modal
            const modalData = collectAllModalData(); // Hàm này từ edit_modal.js

            if (!modalData || Object.keys(modalData).length === 0) {
                Swal.fire('Không có dữ liệu', 'Không có dữ liệu trong modal để xuất.', 'info');
                return;
            }

            try {
                // 2. Tạo nội dung XML từ dữ liệu modal
                // Hàm generateXmlFromModalData (từ export_xml.js) sẽ gọi createXMLContentForType (từ export_xml.js)
                const xmlContent = generateXmlFromModalData(modalData);

                // 3. Lấy timestamp và tạo tên file (từ export_xml.js hoặc tabulator_xml_func.js)
                const timestamp = getFormattedTimestamp();
                const filename = `Modal_Tonghop_${maLK}_${timestamp}.xml`;

                // 4. Tải file XML (từ export_xml.js hoặc tabulator_xml_func.js)
                downloadXmlFile(xmlContent, filename);

                Swal.fire({
                    icon: 'success',
                    title: 'Xuất thành công!',
                    text: `File ${filename} đã được tạo và tải xuống.`
                });
            } catch (error) {
                console.error("Lỗi khi xuất XML từ modal:", error);
                Swal.fire('Lỗi', 'Đã xảy ra lỗi trong quá trình xuất XML: ' + error.message, 'error');
            }
        });

        // Nút Xuất XML chính
        $('#exportMainXmlBtn').on('click', function() {
            console.log("Export XML: Checking data source...");
            console.log("Export XML: isDataFromXML:", window.isDataFromXML);
            console.log("Export XML: currentXMLData:", window.currentXMLData);

            // Kiểm tra xem có dữ liệu trong tables không
            let hasTableData = false;
            let allTableData = {};

            // Kiểm tra dữ liệu trong các tables
            for (let i = 0; i <= 15; i++) {
                const xmlType = `XML${i}`;
                const tableElementId = xmlType.toLowerCase() + '-table';
                const tableElement = document.getElementById(tableElementId);

                if (tableElement && tableElement._tabulator) {
                    try {
                        const tableData = tableElement._tabulator.getData();
                        if (tableData && tableData.length > 0) {
                            allTableData[xmlType] = tableData;
                            hasTableData = true;
                            console.log(`Export XML: Found ${tableData.length} records in ${xmlType}`);
                        }
                    } catch (error) {
                        console.warn(`Export XML: Error getting data from ${xmlType}:`, error);
                    }
                }
            }

            console.log("Export XML: Total table data:", allTableData);
            console.log("Export XML: Has table data:", hasTableData);

            if (hasTableData) {
                // TH1: Có dữ liệu trong tables (từ XML upload hoặc database load)
                console.log("Export XML: Exporting data from Tabulator tables");

                if (window.isDataFromXML) {
                    console.log("Export XML: Data source is XML preview");
                } else {
                    console.log("Export XML: Data source is database");
                }

                // Export dữ liệu từ tables
                exportDataFromTables(allTableData);

            } else {
                // TH2: Không có dữ liệu trong tables
                console.log("Export XML: No data in tables, showing message");
                Swal.fire({
                    icon: 'info',
                    title: 'Không có dữ liệu',
                    text: 'Vui lòng tải dữ liệu từ XML hoặc CSDL trước khi xuất.',
                    confirmButtonText: 'OK'
                });
            }
            for (let i = 0; i <= 15; i++) {
                const xmlTypeKey = 'XML' + i;
                if (window.TabulatorXMLTables && window.TabulatorXMLTables.getTableData) {
                    const tableData = window.TabulatorXMLTables.getTableData(xmlTypeKey);
                    if (tableData && tableData.length > 0) {
                        allTableData[xmlTypeKey] = tableData;
                        hasDataToExport = true;
                    }
                }
            }
        });
    });

    // Mặc định "Từ ngày" và "Đến ngày" là ngày hiện tại
    var today = new Date();
    var dd = String(today.getDate()).padStart(2, '0');
    var mm = String(today.getMonth() + 1).padStart(2, '0'); //January is 0!
    var yyyy = today.getFullYear();
    var currentDate = yyyy + '-' + mm + '-' + dd;
    $('#filter_from_date').val(currentDate);
    $('#filter_to_date').val(currentDate);
    $('#savePreviewDataBtn').hide(); // Mặc định ẩn nút Lưu XML

    // Reset filters functionality
    $('#reset_filters').on('click', function() {
        // Reset all filter inputs
        $('#filter_object_type').val('');
        $('#filter_kcb_type').val('');
        $('#filter_time_type').val('ngayTao'); // Reset to default
        $('#filter_from_date').val(currentDate);
        $('#filter_to_date').val(currentDate);

        // Clear global filters
        window.currentGlobalFilters = null;

        // Show notification
        if (typeof showNotification === 'function') {
            showNotification('Đã xóa tất cả bộ lọc', 'info');
        }

        // Optionally auto-apply filters after clearing
        // $('#apply_filters').trigger('click');
    });

    // Debounced filter application for better performance
    const debouncedApplyFilters = window.AsyncUtils ?
        window.AsyncUtils.debounce(function() {
            $('#apply_filters').trigger('click');
        }, 500) :
        function() { $('#apply_filters').trigger('click'); };

    // Update summary when XML1 data changes
    $(document).on('tabulator-data-loaded', '#xml1-table', function() {
        updateSummaryInfo();
    });

    // Tab switching events
    $('.nav-tabs a').on('shown.bs.tab', function (e) {
        if (typeof updateTabTitles === 'function') {
            updateTabTitles();
        }
    });

    // Modal tab switching events
    $('#editPreviewModal .nav-tabs a').on('shown.bs.tab', function (e) {
        updateModalTabTitles();
    });

    // Function to update modal tab titles
    function updateModalTabTitles() {
        $('#editPreviewModal .nav-link').each(function() {
            var $this = $(this);
            if ($this.hasClass('active')) {
                $this.find('.tab-short-title').hide();
                $this.find('.tab-full-title').show();
            } else {
                $this.find('.tab-short-title').show();
                $this.find('.tab-full-title').hide();
            }
        });
    }

    // Initialize modal tab titles when modal is shown
    $('#editPreviewModal').on('shown.bs.modal', function() {
        updateModalTabTitles();
    });

    // Update summary when switching to XML1 tab
    $('#xml1-tab').on('shown.bs.tab', function() {
        setTimeout(updateSummaryInfo, 100);
    });

    // Handle XML file upload button
    $('#uploadXmlBtn').on('click', function() {
        const files = $('#xml_file')[0].files;
        if (files.length > 0) {
            if (typeof handleXMLUpload === 'function') {
                handleXMLUpload(files);
                $('#importXmlModal').modal('hide');
            } else {
                console.error('handleXMLUpload function not available');
            }
        } else {
            if (typeof showNotification === 'function') {
                showNotification('Vui lòng chọn ít nhất một file XML!', 'warning');
            } else {
                alert('Vui lòng chọn ít nhất một file XML!');
            }
        }
    });

    // Update file label when files are selected
    $('#xml_file').on('change', function() {
        const files = this.files;
        if (files.length > 0) {
            const fileNames = Array.from(files).map(f => f.name).join(', ');
            $(this).next('.custom-file-label').text(fileNames);
        }
    });

    // Xử lý xuất Excel
    $('#exportExcelBtn').on('click', function() {
        exportToExcel();
    });

// Hàm xuất Excel
function exportToExcel() {
    // Kiểm tra nguồn dữ liệu
    const isFromXMLUpload = window.currentDataSource === 'imported_xml';

    if (isFromXMLUpload) {
        // Xuất từ dữ liệu Tabulator (XML upload)
        exportTabulatorDataToExcel();
    } else {
        // Xuất từ database với tất cả các trường
        exportDatabaseDataToExcel();
    }
}

// Xuất dữ liệu từ Tabulator (XML upload mode)
function exportTabulatorDataToExcel() {
    console.log('Exporting Tabulator data to Excel...');

    // Kiểm tra xem có thư viện XLSX không
    if (typeof XLSX === 'undefined') {
        Swal.fire({
            icon: 'error',
            title: 'Thiếu thư viện!',
            text: 'Thư viện XLSX chưa được tải. Vui lòng tải lại trang.'
        });
        return;
    }

    // Tạo workbook mới
    const wb = XLSX.utils.book_new();

    // Danh sách các tab cần xuất (XML0-XML15)
    const tabsToExport = ['xml0', 'xml1', 'xml2', 'xml3', 'xml4', 'xml5', 'xml6', 'xml7', 'xml8', 'xml9', 'xml10', 'xml11', 'xml12', 'xml13', 'xml14', 'xml15'];
    let hasData = false;

    tabsToExport.forEach(tabName => {
        const tableId = tabName + '-table';
        const tableElement = document.getElementById(tableId);

        if (tableElement && tableElement._tabulator) {
            const data = tableElement._tabulator.getData();

            if (data && data.length > 0) {
                hasData = true;

                // Lấy columns config để có header đúng
                const columns = tableElement._tabulator.getColumnDefinitions();

                // Tạo header từ columns
                const headers = columns
                    .filter(col => col.field && col.field !== 'actions') // Loại bỏ cột actions
                    .map(col => col.title || col.field);

                // Tạo data rows
                const rows = data.map(row => {
                    return columns
                        .filter(col => col.field && col.field !== 'actions')
                        .map(col => {
                            let value = row[col.field];
                            // Format số nếu cần
                            if (typeof value === 'number' && col.formatter === 'money') {
                                return value;
                            }
                            return value || '';
                        });
                });

                // Tạo worksheet
                const wsData = [headers, ...rows];
                const ws = XLSX.utils.aoa_to_sheet(wsData);

                // Thêm worksheet vào workbook
                XLSX.utils.book_append_sheet(wb, ws, tabName.toUpperCase());

                console.log(`Added ${tabName.toUpperCase()} sheet with ${data.length} rows`);
            }
        }
    });

    if (!hasData) {
        Swal.fire({
            icon: 'warning',
            title: 'Không có dữ liệu!',
            text: 'Không có dữ liệu nào để xuất Excel.'
        });
        return;
    }

    // Xuất file
    const fileName = `XML4750_Upload_${new Date().toISOString().slice(0, 10)}.xlsx`;
    XLSX.writeFile(wb, fileName);

    Swal.fire({
        icon: 'success',
        title: 'Xuất Excel thành công!',
        text: `File ${fileName} đã được tải xuống.`,
        timer: 3000
    });
}

// Xuất dữ liệu từ database (tất cả các trường)
function exportDatabaseDataToExcel() {
    console.log('Exporting database data to Excel...');

    Swal.fire({
        title: 'Đang xuất dữ liệu...',
        text: 'Vui lòng đợi trong khi hệ thống xuất dữ liệu từ cơ sở dữ liệu.',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    // Lấy filter hiện tại
    const currentFilters = getCurrentFilters();

    // Gọi API để xuất Excel
    $.ajax({
        url: '{% url "xml4750:export_excel" %}',
        method: 'POST',
        data: {
            'csrfmiddlewaretoken': $('[name=csrfmiddlewaretoken]').val(),
            'filters': JSON.stringify(currentFilters)
        },
        xhrFields: {
            responseType: 'blob' // Quan trọng để nhận file
        },
        success: function(data, status, xhr) {
            Swal.close();

            // Tạo URL để download
            const blob = new Blob([data], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            });
            const url = window.URL.createObjectURL(blob);

            // Tạo link download
            const a = document.createElement('a');
            a.href = url;
            a.download = `XML4750_Database_${new Date().toISOString().slice(0, 10)}.xlsx`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            Swal.fire({
                icon: 'success',
                title: 'Xuất Excel thành công!',
                text: 'File đã được tải xuống từ cơ sở dữ liệu.',
                timer: 3000
            });
        },
        error: function(xhr, status, error) {
            Swal.close();
            console.error('Export error:', error);
            Swal.fire({
                icon: 'error',
                title: 'Lỗi xuất Excel!',
                text: 'Có lỗi xảy ra khi xuất dữ liệu: ' + error
            });
        }
    });
}

// Lấy filter hiện tại
function getCurrentFilters() {
    return {
        ma_lien_ket: $('#filter_ma_lien_ket').val() || '',
        ho_ten: $('#filter_ho_ten').val() || '',
        ma_the_bhyt: $('#filter_ma_the_bhyt').val() || '',
        ngay_vao_from: $('#filter_ngay_vao_from').val() || '',
        ngay_vao_to: $('#filter_ngay_vao_to').val() || '',
        ma_benh_chinh: $('#filter_ma_benh_chinh').val() || '',
        ma_cskcb: $('#filter_ma_cskcb').val() || ''
    };
}

// Hàm xóa trực tiếp từ Tabulator cho dữ liệu preview bằng rowIndex
function deleteRowDirectlyFromTabulatorByIndex(type, rowIndex) {
    try {
        console.log(`Deleting ${type} row at index ${rowIndex}`);

        if (type === 'XML1') {
            // XML1: Xóa toàn bộ hồ sơ theo maLK từ tất cả bảng (XML1-XML15)
            // Trước tiên lấy maLK từ dòng XML1 được chọn
            let xml1TableElement = document.getElementById('xml1-table');
            if (xml1TableElement && xml1TableElement._tabulator) {
                let allRows = xml1TableElement._tabulator.getRows();
                if (allRows[rowIndex]) {
                    let rowData = allRows[rowIndex].getData();
                    let maLK = rowData.maLK || rowData.ma_lk;

                    if (maLK) {
                        let deletedCount = 0;
                        // Xóa từ tất cả bảng XML1-XML15
                        for (let i = 1; i <= 15; i++) {
                            let xmlType = 'XML' + i;
                            let tableElement = document.getElementById(xmlType.toLowerCase() + '-table');
                            if (tableElement && tableElement._tabulator) {
                                let rowsToDelete = tableElement._tabulator.getRows().filter(row => {
                                    const data = row.getData();
                                    return (data.maLK === maLK || data.ma_lk === maLK);
                                });
                                rowsToDelete.forEach(row => {
                                    row.delete();
                                    deletedCount++;
                                });
                            }
                        }
                        Swal.fire('Đã xóa!', `Đã xóa toàn bộ hồ sơ (${deletedCount} bản ghi) khỏi xem trước.`, 'success');
                    }
                }
            }
        } else {
            // Xóa record đơn lẻ theo rowIndex
            let tableElement = document.getElementById(type.toLowerCase() + '-table');
            if (tableElement && tableElement._tabulator) {
                let allRows = tableElement._tabulator.getRows();
                if (allRows[rowIndex]) {
                    allRows[rowIndex].delete();
                    Swal.fire('Đã xóa!', `Đã xóa bản ghi ${type} khỏi xem trước.`, 'success');

                    // Cập nhật summary
                    setTimeout(() => {
                        updateSummaryAfterDelete(type);
                    }, 100);
                } else {
                    console.error(`Row at index ${rowIndex} not found in ${type} table`);
                    Swal.fire('Lỗi!', 'Không tìm thấy dòng cần xóa.', 'error');
                }
            }
        }
    } catch (error) {
        console.error('Error deleting row by index:', error);
        Swal.fire('Lỗi!', 'Đã xảy ra lỗi khi xóa dòng.', 'error');
    }
}

// Hàm xóa trực tiếp từ Tabulator (fallback) - cải tiến với row-index
function deleteRowDirectlyFromTabulator(type, id, maLK, ngayTao, rowIndex) {
    try {
        if (type === 'XML1' && maLK) {
            // XML1: Xóa toàn bộ hồ sơ theo maLK từ tất cả bảng (XML1-XML15)
            let deletedCount = 0;
            for (let i = 1; i <= 15; i++) {
                let xmlType = 'XML' + i;
                let tableElement = document.getElementById(xmlType.toLowerCase() + '-table');
                if (tableElement && tableElement._tabulator) {
                    let rowsToDelete = tableElement._tabulator.getRows().filter(row => {
                        const rowData = row.getData();
                        const matchMaLK = rowData.maLK === maLK || rowData.ma_lk === maLK;
                        const matchNgayTao = !ngayTao || rowData.ngayTao === ngayTao || rowData.ngay_tao === ngayTao;
                        return matchMaLK && matchNgayTao;
                    });
                    rowsToDelete.forEach(row => {
                        row.delete();
                        deletedCount++;
                    });
                }
            }
            Swal.fire('Đã xóa!', `Đã xóa toàn bộ hồ sơ (${deletedCount} bản ghi) khỏi xem trước.`, 'success');
        } else {
            // Xóa record đơn lẻ - ưu tiên sử dụng row-index
            let tableElement = document.getElementById(type.toLowerCase() + '-table');
            if (tableElement && tableElement._tabulator) {
                let rowToDelete = null;
                
                // Thử xóa theo row index trước (chính xác hơn)
                if (rowIndex !== undefined && rowIndex !== null) {
                    try {
                        let allRows = tableElement._tabulator.getRows();
                        if (allRows[rowIndex]) {
                            rowToDelete = allRows[rowIndex];
                            console.log(`Found row by index ${rowIndex}`);
                        }
                    } catch (error) {
                        console.warn('Could not get row by index:', error);
                    }
                }
                
                // Nếu không tìm được theo index, thử theo ID
                if (!rowToDelete && id) {
                    try {
                        rowToDelete = tableElement._tabulator.getRow(id);
                        console.log(`Found row by ID ${id}`);
                    } catch (error) {
                        console.warn('Could not get row by ID:', error);
                    }
                }
                
                // Nếu vẫn không tìm được, thử tìm theo maLK và ngayTao
                if (!rowToDelete && maLK) {
                    try {
                        let matchingRows = tableElement._tabulator.getRows().filter(row => {
                            const rowData = row.getData();
                            const matchMaLK = rowData.maLK === maLK || rowData.ma_lk === maLK;
                            const matchNgayTao = !ngayTao || rowData.ngayTao === ngayTao || rowData.ngay_tao === ngayTao;
                            const matchId = !id || rowData.id == id;
                            return matchMaLK && matchNgayTao && matchId;
                        });
                        
                        if (matchingRows.length > 0) {
                            rowToDelete = matchingRows[0];
                            console.log(`Found row by maLK/ngayTao`);
                        }
                    } catch (error) {
                        console.warn('Could not find row by maLK/ngayTao:', error);
                    }
                }
                
                if (rowToDelete) {
                    rowToDelete.delete();
                    Swal.fire('Đã xóa!', `Đã xóa bản ghi ${type} khỏi xem trước.`, 'success');
                } else {
                    Swal.fire('Lỗi!', 'Không tìm thấy dòng để xóa.', 'error');
                }
            }
        }
        
        // Cập nhật summary
        setTimeout(() => {
            updateSummaryAfterDelete(type);
        }, 100);
        
    } catch (error) {
        console.error('Error in deleteRowDirectlyFromTabulator:', error);
        Swal.fire('Lỗi!', 'Đã xảy ra lỗi khi xóa dòng: ' + error.message, 'error');
    }
}

// Hàm xóa từ database - cải tiến
function deleteRowFromDatabase(type, id, maLK, ngayTao, rowIndex) {
    $.ajax({
        url: '/xml4750/delete/' + type + '/' + id + '/',
        type: 'POST',
        data: { 'csrfmiddlewaretoken': $('[name=csrfmiddlewaretoken]').val() },
        success: function(response) {
            if (response.success) {
                let successMessage = response.message || 'Dữ liệu đã được xóa thành công.';
                
                // Xóa dòng khỏi tabulator sau khi server xóa thành công
                try {
                        if (type === 'XML1' && maLK) {
                            // XML1: Xóa toàn bộ hồ sơ theo maLK và ngayTao từ tất cả bảng (XML1-XML15)
                            let totalDeletedRows = 0;
                            
                            for (let i = 1; i <= 15; i++) {
                                let xmlType = 'XML' + i;
                                let tableElement = document.getElementById(xmlType.toLowerCase() + '-table');
                                
                                if (tableElement && tableElement._tabulator) {
                                    // Lấy tất cả rows cần xóa trước
                                    let rowsToDelete = tableElement._tabulator.getRows().filter(row => {
                                        const rowData = row.getData();
                                        const matchMaLK = rowData.maLK === maLK || rowData.ma_lk === maLK;
                                        const matchNgayTao = !ngayTao || rowData.ngayTao === ngayTao || rowData.ngay_tao === ngayTao;
                                        return matchMaLK && matchNgayTao;
                                    });
                                    
                                    // Xóa từng row
                                    rowsToDelete.forEach(row => {
                                        try {
                                            row.delete();
                                            totalDeletedRows++;
                                        } catch (error) {
                                            console.warn(`Error deleting row from ${xmlType}:`, error);
                                        }
                                    });
                                    
                                    console.log(`Deleted ${rowsToDelete.length} rows from ${xmlType}`);
                                }
                            }
                            
                            if (totalDeletedRows > 0) {
                                successMessage += ` (Đã xóa ${totalDeletedRows} bản ghi khỏi giao diện)`;
                            }
                            
                        } else {
                            // Xóa record đơn lẻ
                            let tableElement = document.getElementById(type.toLowerCase() + '-table');
                            if (tableElement && tableElement._tabulator) {
                                try {
                                    // Thử xóa theo array index trước (chính xác hơn)
                                    let rowToDelete = null;
                                    if (rowIndex !== undefined && rowIndex !== null && !isNaN(rowIndex) && rowIndex >= 0) {
                                        try {
                                            let allRows = tableElement._tabulator.getRows();
                                            if (allRows[rowIndex]) {
                                                rowToDelete = allRows[rowIndex];
                                                console.log(`Found row by array index ${rowIndex}`);
                                            }
                                        } catch (error) {
                                            console.warn('Could not get row by array index:', error);
                                        }
                                    }

                                    // Nếu không tìm được theo index, thử tìm theo ID
                                    if (!rowToDelete && id) {
                                        try {
                                            rowToDelete = tableElement._tabulator.getRow(id);
                                            console.log(`Found row by ID ${id}`);
                                        } catch (error) {
                                            console.warn('Could not get row by ID:', error);
                                        }
                                    }

                                    // Nếu vẫn không tìm được, thử tìm theo maLK và ngayTao
                                    if (!rowToDelete && maLK) {
                                        try {
                                            let matchingRows = tableElement._tabulator.getRows().filter(row => {
                                                const rowData = row.getData();
                                                const matchMaLK = rowData.maLK === maLK || rowData.ma_lk === maLK;
                                                const matchNgayTao = !ngayTao || rowData.ngayTao === ngayTao || rowData.ngay_tao === ngayTao;
                                                const matchId = !id || rowData.id == id;
                                                return matchMaLK && matchNgayTao && matchId;
                                            });

                                            if (matchingRows.length > 0) {
                                                rowToDelete = matchingRows[0];
                                                console.log(`Found row by maLK/ngayTao`);
                                            }
                                        } catch (error) {
                                            console.warn('Could not find row by maLK/ngayTao:', error);
                                        }
                                    }

                                    if (rowToDelete) {
                                        rowToDelete.delete();
                                        console.log(`Deleted row from ${type}`);
                                        successMessage += ' (Đã cập nhật giao diện)';
                                    } else {
                                        console.log(`Row not found in ${type} table`);
                                    }
                                } catch (error) {
                                    console.warn(`Error deleting row from ${type} table:`, error);
                                }
                            }
                        }

                    } catch (error) {
                        console.error('Error removing rows from Tabulator:', error);
                        successMessage += ' (Dữ liệu đã được xóa từ server)';
                    }
                
                // Cập nhật summary functions - đảm bảo chạy sau khi xóa
                setTimeout(() => {
                    updateSummaryAfterDelete(type);
                }, 100);
                
                Swal.fire('Đã xóa!', successMessage, 'success');
                
            } else {
                Swal.fire('Lỗi!', response.message || 'Đã xảy ra lỗi khi xóa dữ liệu.', 'error');
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX Error:', { xhr, status, error });
            Swal.fire('Lỗi!', 'Đã xảy ra lỗi khi xóa dữ liệu trên máy chủ.', 'error');
        }
    });
}

// Hàm helper để cập nhật summary sau khi xóa - cải tiến
function updateSummaryAfterDelete(type) {
    try {
        // Kiểm tra xem table có tồn tại không trước khi cập nhật summary
        let tableElement = document.getElementById(type.toLowerCase() + '-table');
        if (!tableElement || !tableElement._tabulator) {
            console.log(`Table ${type} not found or not initialized, skipping summary update`);
            return;
        }
        
        switch(type) {
            case 'XML1':
                if (typeof updateXML1Summary === 'function') {
                    updateXML1Summary();
                }
                break;
            case 'XML2':
                if (typeof updateXML2Summary === 'function') {
                    updateXML2Summary();
                }
                break;
            case 'XML3':
                if (typeof updateXML3Summary === 'function') {
                    updateXML3Summary();
                }
                break;
            case 'XML4':
                if (typeof updateXML4Summary === 'function') {
                    updateXML4Summary();
                }
                break;
            case 'XML5':
                if (typeof updateXML5Summary === 'function') {
                    updateXML5Summary();
                }
                break;
            // Thêm các case khác nếu cần
            default:
                console.log(`No summary update function for ${type}`);
        }
    } catch (error) {
        console.error('Error updating summary:', error);
    }
}

// Hàm helper để kiểm tra và xử lý trường hợp table bị refresh
function handleTableRefreshAfterDelete(type, id) {
    try {
        let tableElement = document.getElementById(type.toLowerCase() + '-table');
        if (tableElement && tableElement._tabulator) {
            // Kiểm tra số lượng rows hiện tại
            let currentRowCount = tableElement._tabulator.getRows().length;
            console.log(`${type} table current row count: ${currentRowCount}`);
            
            // Nếu table trống hoặc gần như trống, có thể đã bị refresh
            if (currentRowCount === 0) {
                console.log(`${type} table appears to be empty after delete`);
                return true; // Table đã được refresh
            }
            
            // Kiểm tra xem row cụ thể có còn tồn tại không
            try {
                let row = tableElement._tabulator.getRow(id);
                return false; // Row vẫn còn
            } catch (error) {
                return true; // Row không còn tồn tại
            }
        }
        return false;
    } catch (error) {
        console.error('Error checking table refresh status:', error);
        return false;
    }
}

// Hàm helper để đảm bảo data attributes được thiết lập đúng cho các button - cải tiến
function ensureDeleteButtonDataAttributes() {
    $('.delete-xml').each(function(index) {
        var button = $(this);
        var rowElement = button.closest('.tabulator-row');
        
        if (rowElement.length && rowElement[0]._tabulator_row) {
            var rowData = rowElement[0]._tabulator_row.getData();
            var rowPosition = rowElement[0]._tabulator_row.getPosition();
            
            // Thiết lập data attributes nếu chưa có
            if (!button.data('ma-lk') && (rowData.maLK || rowData.ma_lk)) {
                button.attr('data-ma-lk', rowData.maLK || rowData.ma_lk);
            }
            if (!button.data('ngay-tao') && (rowData.ngayTao || rowData.ngay_tao)) {
                button.attr('data-ngay-tao', rowData.ngayTao || rowData.ngay_tao);
            }
            if (!button.data('id') && rowData.id) {
                button.attr('data-id', rowData.id);
            }
            
            // Thiết lập row-index
            if (!button.data('row-index')) {
                button.attr('data-row-index', rowPosition);
            }
        }
    });
}

// Additional initialization after main document ready
$(document).ready(async function() {
    // Hiển thị loading screen
    showPageLoadingScreen();

    // Initialize XML4750 interface (without database connection)
    if (typeof initializeXMLInterface === 'function') {
        initializeXMLInterface();
    }

    // Initialize tab titles
    if (typeof updateTabTitles === 'function') {
        updateTabTitles();
    }

    // Set initial data source state (but don't load data)
    if (typeof updateDataSourceState === 'function') {
        updateDataSourceState('database');
    }

    // Initialize Tabulator tables configuration only (no data loading)
    if (window.TabulatorXMLTables && window.TabulatorXMLTables.initializeTablesConfigOnly) {
        try {
            await window.TabulatorXMLTables.initializeTablesConfigOnly();
        } catch (error) {
            console.error("Error initializing Tabulator tables configuration:", error);
        }
    } else if (window.TabulatorXMLTables && window.TabulatorXMLTables.initializeAllTables) {
        try {
            // Set flag to prevent automatic data loading
            window.allowInitialTabulatorLoad = false;
            await window.TabulatorXMLTables.initializeAllTables();
        } catch (error) {
            console.error("Error initializing Tabulator tables:", error);
        }
    } else {
        console.warn("TabulatorXMLTables initialization methods not available");
    }
    initBHYTCardManagement();
    // Đảm bảo data attributes được thiết lập khi trang load
    setTimeout(ensureDeleteButtonDataAttributes, 1000);
    // Thiết lập lại data attributes khi table được cập nhật
    $(document).on('tabulator-table-built tabulator-data-loaded', function() {
        setTimeout(ensureDeleteButtonDataAttributes, 500);
    });
    // Đánh dấu rằng khởi tạo đã hoàn tất
    window.initializationCompleted = true;
});

// ===== Validation =======================
// Thêm nút validate vào các bảng Tabulator
        function addValidationColumn() {
            // Thêm cột validation status vào tất cả các bảng Tabulator
            for (let i = 1; i <= 15; i++) {
                const tableId = `xml${i}-table`;
                const tableElement = document.getElementById(tableId);
                const table = tableElement && tableElement._tabulator ? tableElement._tabulator : null;
                
                if (table) {
                    // Kiểm tra xem cột đã tồn tại chưa
                    const columns = table.getColumns();
                    const hasValidationColumn = columns.some(col => col.getField() === 'validationStatus');
                    
                    if (!hasValidationColumn) {
                        // Thêm cột validation status vào đầu bảng (sau cột checkbox nếu có)
                        table.addColumn({
                            title: '<i class="fas fa-check-circle"></i>',
                            field: 'validationStatus',
                            headerSort: false,
                            width: 40,
                            cssClass: 'validation-status-column',
                            formatter: function(cell, formatterParams, onRendered) {
                                const data = cell.getRow().getData();
                                
                                if (data._validationResult) {
                                    if (data._validationResult.valid) {
                                        return '<i class="fas fa-check-circle validation-status-valid" title="Dữ liệu hợp lệ"></i>';
                                    } else {
                                        const errorCount = data._validationResult.errors.length;
                                        const hasBlockingError = data._validationResult.hasBlockingError;
                                        const icon = hasBlockingError ? 'fas fa-times-circle' : 'fas fa-exclamation-circle';
                                        
                                        return `<i class="${icon} validation-status-invalid validation-tooltip" 
                                                  title="Có ${errorCount} lỗi" 
                                                  data-toggle="tooltip" 
                                                  data-html="true" 
                                                  data-placement="right"></i>`;
                                    }
                                }
                                
                                return '';
                            },
                            cellClick: function(e, cell) {
                                const data = cell.getRow().getData();
                                
                                if (data._validationResult && !data._validationResult.valid) {
                                    showValidationErrors(data._validationResult.errors, data.maLK);
                                }
                            }
                                                }, true);
                    }
                }
            }
        }
        
        // Hiển thị modal lỗi validation
        function showValidationErrors(errors, maLK) {
            // Phân loại lỗi theo mức độ nghiêm trọng
            const blockingErrors = errors.filter(error => error.blocking);
            const warningErrors = errors.filter(error => !error.blocking);

            let errorHtml = `<div class="validation-errors">
                <div class="alert alert-info mb-3">
                    <strong>Tổng quan:</strong>
                    <span class="text-danger">${blockingErrors.length} lỗi nghiêm trọng</span>,
                    <span class="text-warning">${warningErrors.length} cảnh báo</span>
                </div>`;

            if (blockingErrors.length > 0) {
                errorHtml += `
                    <h6 class="text-danger"><i class="fas fa-times-circle"></i> Lỗi nghiêm trọng (chặn lưu dữ liệu)</h6>
                    <table class="table table-bordered table-striped table-sm mb-3">
                        <thead class="thead-dark">
                            <tr>
                                <th width="15%">XML</th>
                                <th width="25%">Trường</th>
                                <th width="45%">Mô tả lỗi</th>
                                <th width="15%">Quy tắc</th>
                            </tr>
                        </thead>
                        <tbody>`;

                blockingErrors.forEach(error => {
                    errorHtml += `<tr class="table-danger">
                        <td><strong>${error.xmlType || 'N/A'}</strong></td>
                        <td><code>${error.fieldName || 'N/A'}</code></td>
                        <td>${error.message}</td>
                        <td><span class="badge badge-danger">Chặn lưu</span></td>
                    </tr>`;
                });

                errorHtml += `</tbody></table>`;
            }

            if (warningErrors.length > 0) {
                errorHtml += `
                    <h6 class="text-warning"><i class="fas fa-exclamation-triangle"></i> Cảnh báo (không chặn lưu)</h6>
                    <table class="table table-bordered table-striped table-sm">
                        <thead class="thead-light">
                            <tr>
                                <th width="15%">XML</th>
                                <th width="25%">Trường</th>
                                <th width="45%">Mô tả cảnh báo</th>
                                <th width="15%">Quy tắc</th>
                            </tr>
                        </thead>
                        <tbody>`;

                warningErrors.forEach(error => {
                    errorHtml += `<tr class="table-warning">
                        <td><strong>${error.xmlType || 'N/A'}</strong></td>
                        <td><code>${error.fieldName || 'N/A'}</code></td>
                        <td>${error.message}</td>
                        <td><span class="badge badge-warning">Cảnh báo</span></td>
                    </tr>`;
                });

                errorHtml += `</tbody></table>`;
            }

            errorHtml += `</div>`;

            // Xác định icon và title dựa trên mức độ nghiêm trọng
            const icon = blockingErrors.length > 0 ? 'error' : 'warning';
            const title = blockingErrors.length > 0 ?
                `🚫 Lỗi nghiêm trọng - Mã LK: ${maLK}` :
                `⚠️ Cảnh báo - Mã LK: ${maLK}`;

            Swal.fire({
                title: title,
                html: errorHtml,
                icon: icon,
                width: '900px',
                confirmButtonText: 'Đóng',
                customClass: {
                    popup: 'validation-error-popup'
                }
            });
        }
        
        // Thực hiện kiểm tra dữ liệu
        function validateData() {
            // Kiểm tra xem đã tải module validation chưa
            if (!window.XMLValidation) {
                Swal.fire('Lỗi', 'Không thể tải module kiểm tra dữ liệu', 'error');
                return;
            }
            
            // Hiển thị loading
            Swal.fire({
                title: 'Đang kiểm tra dữ liệu...',
                text: 'Vui lòng đợi trong giây lát',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
            
            // Tải cấu hình validation
            window.XMLValidation.loadConfig()
                .then(() => {
                    // Thu thập tất cả dữ liệu từ các bảng
                    const allData = {};
                    
                    for (let i = 1; i <= 15; i++) {
                        const tableId = `xml${i}-table`;
                        const tableElement = document.getElementById(tableId);
                        const table = tableElement && tableElement._tabulator ? tableElement._tabulator : null;
                        
                        if (table) {
                            const xmlType = `XML${i}`;
                            const data = table.getData();
                            
                            if (data && data.length > 0) {
                                allData[xmlType] = data;
                            }
                        }
                    }
                    
                    // Kiểm tra dữ liệu
                    const validationResults = window.XMLValidation.validateAllData(allData);
                    
                    // Cập nhật kết quả vào các bảng
                    let totalErrors = 0;
                    let totalBlockingErrors = 0;
                    
                    for (let i = 1; i <= 15; i++) {
                        const tableId = `xml${i}-table`;
                        const tableElement = document.getElementById(tableId);
                        const table = tableElement && tableElement._tabulator ? tableElement._tabulator : null;
                        
                        if (table) {
                            const xmlType = `XML${i}`;
                            const data = table.getData();
                            
                            if (data && data.length > 0) {
                                // Cập nhật từng dòng
                                data.forEach(row => {
                                    const maLK = row.maLK;
                                    
                                    if (maLK && validationResults[maLK]) {
                                        // Lọc lỗi cho loại XML này
                                        const xmlErrors = validationResults[maLK].errors.filter(
                                            error => !error.xmlType || error.xmlType === xmlType
                                        );
                                        
                                        // Cập nhật kết quả validation
                                        row._validationResult = {
                                            valid: xmlErrors.length === 0,
                                            hasBlockingError: xmlErrors.some(error => error.blocking),
                                            errors: xmlErrors
                                        };
                                        
                                        // Cập nhật tổng số lỗi
                                        totalErrors += xmlErrors.length;
                                        if (row._validationResult.hasBlockingError) {
                                            totalBlockingErrors++;
                                        }
                                    } else {
                                        // Không có kết quả validation
                                        row._validationResult = {
                                            valid: true,
                                            hasBlockingError: false,
                                            errors: []
                                        };
                                    }
                                });
                                
                                // Cập nhật bảng
                                table.setData(data);
                                
                                // Thêm CSS cho các dòng có lỗi
                                table.getRows().forEach(row => {
                                    const data = row.getData();
                                    
                                    if (data._validationResult && !data._validationResult.valid) {
                                        row.getElement().classList.add('validation-error-row');
                                    } else {
                                        row.getElement().classList.remove('validation-error-row');
                                    }
                                });
                            }
                        }
                    }
                    
                    // Hiển thị kết quả
                    Swal.close();
                    
                    if (totalErrors === 0) {
                        Swal.fire({
                            title: 'Kiểm tra thành công',
                            text: 'Tất cả dữ liệu đều hợp lệ',
                            icon: 'success',
                            confirmButtonText: 'Đóng'
                        });
                    } else {
                        Swal.fire({
                            title: 'Kết quả kiểm tra',
                            html: `
                                <div class="text-left">
                                    <p>Tổng số lỗi: <strong>${totalErrors}</strong></p>
                                    <p>Lỗi nghiêm trọng: <strong>${totalBlockingErrors}</strong></p>
                                    <p>Lỗi cảnh báo: <strong>${totalErrors - totalBlockingErrors}</strong></p>
                                    <hr>
                                    <p>Nhấp vào biểu tượng <i class="fas fa-times-circle text-danger"></i> hoặc <i class="fas fa-exclamation-circle text-warning"></i> để xem chi tiết lỗi.</p>
                                </div>
                            `,
                            icon: 'warning',
                            confirmButtonText: 'Đóng'
                        });
                    }
                })
                .catch(error => {
                    console.error('Validation error:', error);
                    Swal.fire('Lỗi', 'Không thể kiểm tra dữ liệu: ' + error.message, 'error');
                });
        }
        
        // Thêm sự kiện cho nút validate
        $('#validate_data_btn').click(function() {
            // Kiểm tra xem có dữ liệu không
            let hasData = false;
            
            for (let i = 1; i <= 15; i++) {
                const tableId = `xml${i}-table`;
                const tableElement = document.getElementById(tableId);
                const table = tableElement && tableElement._tabulator ? tableElement._tabulator : null;
                
                if (table && table.getData().length > 0) {
                    hasData = true;
                    break;
                }
            }
            
            if (!hasData) {
                Swal.fire('Thông báo', 'Không có dữ liệu để kiểm tra', 'info');
                return;
            }
            
            // Thêm cột validation nếu chưa có
            addValidationColumn();
            
            // Thực hiện kiểm tra
            validateData();
        });
// ===== End validation ===================

// ===== LOADING SCREEN FUNCTIONS =====

/**
 * Hiển thị loading screen khi trang web đang khởi tạo
 */
function showPageLoadingScreen() {
    Swal.fire({
        title: 'Đang khởi tạo hệ thống...',
        html: `
            <div style="text-align: center;">
                <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                    <span class="sr-only">Loading...</span>
                </div>
                <div style="margin-top: 15px;">
                    <div id="loading-progress-text">Đang tải cấu hình...</div>
                    <div class="progress" style="margin-top: 10px;">
                        <div id="loading-progress-bar" class="progress-bar progress-bar-striped progress-bar-animated"
                             role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
            </div>
        `,
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false,
        didOpen: () => {
            // Bắt đầu quá trình loading
            startLoadingProcess();
        }
    });
}

/**
 * Bắt đầu quá trình loading với các bước
 */
async function startLoadingProcess() {
    const steps = [
        { text: 'Đang tải cấu hình hệ thống...', progress: 10, delay: 300 },
        { text: 'Đang khởi tạo Tabulator...', progress: 25, delay: 500 },
        { text: 'Đang tải danh mục từ CSDL...', progress: 45, delay: 800 },
        { text: 'Đang thiết lập giao diện...', progress: 65, delay: 400 },
        { text: 'Đang chuẩn bị các chức năng...', progress: 80, delay: 300 },
        { text: 'Hoàn tất khởi tạo...', progress: 100, delay: 200 }
    ];

    for (let i = 0; i < steps.length; i++) {
        const step = steps[i];

        // Cập nhật text và progress
        updateLoadingProgress(step.text, step.progress);

        // Đợi một chút để người dùng thấy được tiến trình
        await new Promise(resolve => setTimeout(resolve, step.delay));

        // Thực hiện các tác vụ thực tế tại đây nếu cần
        if (i === 1) {
            // Khởi tạo Tabulator
            await initializeTabulatorIfNeeded();
        } else if (i === 2) {
            // Tải danh mục
            await loadCategoriesIfNeeded();
        } else if (i === 3) {
            // Thiết lập giao diện
            await setupInterfaceIfNeeded();
        }
    }

    // Đợi thêm một chút rồi đóng loading
    setTimeout(() => {
        hidePageLoadingScreen();
    }, 500);
}

/**
 * Cập nhật tiến trình loading
 */
function updateLoadingProgress(text, progress) {
    const progressText = document.getElementById('loading-progress-text');
    const progressBar = document.getElementById('loading-progress-bar');

    if (progressText) {
        progressText.textContent = text;
    }

    if (progressBar) {
        progressBar.style.width = progress + '%';
        progressBar.setAttribute('aria-valuenow', progress);
    }
}

/**
 * Ẩn loading screen
 */
function hidePageLoadingScreen() {
    Swal.close();

    // Hiển thị thông báo hoàn tất
    Swal.fire({
        icon: 'success',
        title: 'Hệ thống đã sẵn sàng!',
        text: 'Bạn có thể bắt đầu sử dụng các chức năng.',
        timer: 2000,
        showConfirmButton: false,
        toast: true,
        position: 'top-end'
    });
}

/**
 * Các hàm helper cho quá trình loading
 */
async function initializeTabulatorIfNeeded() {
    // Kiểm tra xem Tabulator đã được khởi tạo chưa
    if (typeof window.TabulatorXMLTables !== 'undefined') {
        return;
    }
}

async function loadCategoriesIfNeeded() {
    console.log('Đang kiểm tra danh mục...');

    // Có thể gọi API để tải danh mục ở đây
    try {
        // Ví dụ: await loadCategories();
        console.log('Danh mục đã được tải');
    } catch (error) {
        console.warn('Không thể tải danh mục:', error);
    }
}

async function setupInterfaceIfNeeded() {
    // Thiết lập các thành phần giao diện
    console.log('Đang thiết lập giao diện...');

    // Đợi cho đến khi khởi tạo hoàn tất
    let attempts = 0;
    const maxAttempts = 50; // 5 giây

    while (!window.initializationCompleted && attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 100));
        attempts++;
    }

    // Khởi tạo các event listener, select2, etc.
    try {
        // Có thể gọi các hàm setup ở đây
        console.log('Giao diện đã được thiết lập');
    } catch (error) {
        console.warn('Không thể thiết lập giao diện:', error);
    }
}

</script>

<!-- Thêm script cho validation -->
<script src="{% static 'js/xml4750/validation_config.js' %}"></script>
<script src="{% static 'js/xml4750/validation.js' %}"></script>

{% endblock %}
